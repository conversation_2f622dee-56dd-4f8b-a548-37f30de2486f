{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "e486cdcdbc28b79531b3b0b3bf6d578e", "packages": [{"name": "brick/math", "version": "0.12.1", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "f510c0a40911935b77b86859eb5223d58d660df1"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/brick/math/0.12.1/brick-math-0.12.1.zip", "reference": "f510c0a40911935b77b86859eb5223d58d660df1", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^10.1", "vimeo/psalm": "5.16.0"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "bignumber", "brick", "decimal", "integer", "math", "mathematics", "rational"], "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2023-11-29T23:19:16+00:00"}, {"name": "carbonphp/carbon-doctrine-types", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon-doctrine-types.git", "reference": "18ba5ddfec8976260ead6e866180bd5d2f71aa1d"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/carbonphp/carbon-doctrine-types/3.2.0/carbonphp-carbon-doctrine-types-3.2.0.zip", "reference": "18ba5ddfec8976260ead6e866180bd5d2f71aa1d", "shasum": ""}, "require": {"php": "^8.1"}, "conflict": {"doctrine/dbal": "<4.0.0 || >=5.0.0"}, "require-dev": {"doctrine/dbal": "^4.0.0", "nesbot/carbon": "^2.71.0 || ^3.0.0", "phpunit/phpunit": "^10.3"}, "type": "library", "autoload": {"psr-4": {"Carbon\\Doctrine\\": "src/Carbon/Doctrine/"}}, "license": ["MIT"], "authors": [{"name": "KyleKatarn", "email": "<EMAIL>"}], "description": "Types to use Carbon in Doctrine", "keywords": ["carbon", "date", "datetime", "doctrine", "time"], "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2024-02-09T16:56:22+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "31610dbb31faa98e6b5447b62340826f54fbc4e9"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/doctrine/deprecations/1.1.4/doctrine-deprecations-1.1.4.zip", "reference": "31610dbb31faa98e6b5447b62340826f54fbc4e9", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "1.4.10 || 2.0.3", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psr/log": "^1 || ^2 || ^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "src"}}, "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "time": "2024-12-07T21:18:45+00:00"}, {"name": "doctrine/inflector", "version": "2.0.10", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/doctrine/inflector/2.0.10/doctrine-inflector-2.0.10.zip", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^11.0", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^8.5 || ^9.5", "vimeo/psalm": "^4.25 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2024-02-18T20:23:39+00:00"}, {"name": "doctrine/instantiator", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/doctrine/instantiator/1.5.0/doctrine-instantiator-1.5.0.zip", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.16 || ^1", "phpstan/phpstan": "^1.4", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.30 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-12-30T00:15:36+00:00"}, {"name": "doctrine/lexer", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/doctrine/lexer/2.1.1/doctrine-lexer-2.1.1.zip", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^4.11 || ^5.21"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2024-02-05T11:35:39+00:00"}, {"name": "duncan3dc/blade", "version": "4.14.0", "source": {"type": "git", "url": "https://github.com/duncan3dc/blade.git", "reference": "c67d41268adbb56fa7f56cef1ee139aa9059d750"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/duncan3dc/blade/4.14.0/duncan3dc-blade-4.14.0.zip", "reference": "c67d41268adbb56fa7f56cef1ee139aa9059d750", "shasum": ""}, "require": {"illuminate/contracts": "^8.0 || ^9.0 || ^10.0 || ^11.0", "illuminate/events": "^8.0 || ^9.0 || ^10.0 || ^11.0", "illuminate/filesystem": "^8.0 || ^9.0 || ^10.0 || ^11.0", "illuminate/view": "^8.0 || ^9.0 || ^10.0 || ^11.0", "php": "^7.3 || ^8.0"}, "require-dev": {"duncan3dc/object-intruder": "^0.3.0 || ^1.0", "maglnet/composer-require-checker": "^2.0 || ^3.0", "mockery/mockery": "^1.4", "nikic/php-parser": "^4.18", "phpstan/phpstan": "^0.12.100", "phpstan/phpstan-mockery": "^0.12.5", "phpstan/phpstan-phpunit": "^0.12.11", "phpunit/phpunit": "^8.5.36", "squizlabs/php_codesniffer": "^3.6"}, "type": "library", "autoload": {"psr-4": {"duncan3dc\\Laravel\\": "src/"}}, "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "*****************", "homepage": "https://github.com/duncan3dc", "role": "Developer"}], "description": "Use Laravel Blade templates without the full Laravel framework", "homepage": "https://github.com/duncan3dc/blade", "keywords": ["laravel", "template", "templating", "views"], "time": "2024-07-23T11:11:08+00:00"}, {"name": "egulias/email-validator", "version": "3.2.6", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/egulias/email-validator/3.2.6/egulias-email-validator-3.2.6.zip", "reference": "e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7", "shasum": ""}, "require": {"doctrine/lexer": "^1.2|^2", "php": ">=7.2", "symfony/polyfill-intl-idn": "^1.15"}, "require-dev": {"phpunit/phpunit": "^8.5.8|^9.3.3", "vimeo/psalm": "^4"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2023-06-01T07:04:22+00:00"}, {"name": "fig/http-message-util", "version": "1.1.5", "source": {"type": "git", "url": "https://github.com/php-fig/http-message-util.git", "reference": "9d94dc0154230ac39e5bf89398b324a86f63f765"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/fig/http-message-util/1.1.5/fig-http-message-util-1.1.5.zip", "reference": "9d94dc0154230ac39e5bf89398b324a86f63f765", "shasum": ""}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "suggest": {"psr/http-message": "The package containing the PSR-7 interfaces"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Fig\\Http\\Message\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Utility classes and constants for use with PSR-7 (psr/http-message)", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2020-11-24T22:02:12+00:00"}, {"name": "graham-campbell/result-type", "version": "v1.1.3", "source": {"type": "git", "url": "https://github.com/GrahamCampbell/Result-Type.git", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/graham-campbell/result-type/v1.1.3/graham-campbell-result-type-v1.1.3.zip", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3"}, "require-dev": {"phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}, "type": "library", "autoload": {"psr-4": {"GrahamCampbell\\ResultType\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "An Implementation Of The Result Type", "keywords": ["<PERSON>", "Graham<PERSON><PERSON><PERSON>", "Result Type", "Result-Type", "result"], "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/graham-campbell/result-type", "type": "tidelift"}], "time": "2024-07-20T21:45:45+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.9.2", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "d281ed313b989f213357e3be1a179f02196ac99b"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/guzzlehttp/guzzle/7.9.2/guzzlehttp-guzzle-7.9.2.zip", "reference": "d281ed313b989f213357e3be1a179f02196ac99b", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2024-07-24T11:22:20+00:00"}, {"name": "guzzlehttp/promises", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "f9c436286ab2892c7db7be8c8da4ef61ccf7b455"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/guzzlehttp/promises/2.0.4/guzzlehttp-promises-2.0.4.zip", "reference": "f9c436286ab2892c7db7be8c8da4ef61ccf7b455", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2024-10-17T10:06:22+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "a70f5c95fb43bc83f07c9c948baa0dc1829bf201"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/guzzlehttp/psr7/2.7.0/guzzlehttp-psr7-2.7.0.zip", "reference": "a70f5c95fb43bc83f07c9c948baa0dc1829bf201", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2024-07-18T11:15:46+00:00"}, {"name": "hyperf/amqp", "version": "v3.1.50", "source": {"type": "git", "url": "https://github.com/hyperf/amqp.git", "reference": "db5e24822f2dcc3af055b5c985702f59901be380"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/amqp/v3.1.50/hyperf-amqp-v3.1.50.zip", "reference": "db5e24822f2dcc3af055b5c985702f59901be380", "shasum": ""}, "require": {"doctrine/instantiator": "^1.2.0", "hyperf/codec": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/coroutine": "~3.1.0", "hyperf/pool": "~3.1.0", "hyperf/process": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "php-amqplib/php-amqplib": "^3.5", "psr/container": "^1.0 || ^2.0", "psr/event-dispatcher": "^1.0", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "suggest": {"hyperf/di": "Required to use annotations.", "hyperf/event": "Declare queue and start consumers automatically."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Amqp\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Amqp\\": "src/"}}, "license": ["MIT"], "description": "A amqplib for hyperf.", "homepage": "https://hyperf.io", "keywords": ["AMQP", "hyperf", "php"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2025-01-08T08:57:09+00:00"}, {"name": "hyperf/async-queue", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/async-queue.git", "reference": "1cd25666ac1e1f23c9eab6be642e86802a96307b"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/async-queue/v3.1.42/hyperf-async-queue-v3.1.42.zip", "reference": "1cd25666ac1e1f23c9eab6be642e86802a96307b", "shasum": ""}, "require": {"hyperf/codec": "~3.1.0", "hyperf/collection": "~3.1.0", "hyperf/command": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/event-dispatcher": "^1.0"}, "suggest": {"hyperf/di": "Required to use annotations.", "hyperf/event": "Required to dispatch a event.", "hyperf/logger": "Required to use QueueHandleListener.", "hyperf/process": "Auto register the consumer process for server."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\AsyncQueue\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\AsyncQueue\\": "src/"}}, "license": ["MIT"], "description": "A async queue component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["async-queue", "hyperf", "php"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/cache", "version": "v3.1.43", "source": {"type": "git", "url": "https://github.com/hyperf/cache.git", "reference": "1e3cc54cee776c8d32cf40912dee5d366383bc33"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/cache/v3.1.43/hyperf-cache-v3.1.43.zip", "reference": "1e3cc54cee776c8d32cf40912dee5d366383bc33", "shasum": ""}, "require": {"hyperf/codec": "~3.1.0", "hyperf/collection": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0"}, "suggest": {"hyperf/di": "Use cache annotations.", "hyperf/event": "Use listener to delete annotation cache."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Cache\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Cache\\": "src/"}}, "license": ["MIT"], "description": "A cache component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["cache", "hyperf", "php"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-10-09T10:22:39+00:00"}, {"name": "hyperf/code-parser", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/code-parser.git", "reference": "81953c4ea9035ac5f0a4740ae157310ca4e18ff2"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/code-parser/v3.1.42/hyperf-code-parser-v3.1.42.zip", "reference": "81953c4ea9035ac5f0a4740ae157310ca4e18ff2", "shasum": ""}, "require": {"hyperf/collection": "~3.1.0", "hyperf/stringable": "~3.1.0", "hyperf/support": "~3.1.0", "php": ">=8.1"}, "suggest": {"jean85/pretty-package-versions": "Required to use PrettyVersions. (^1.2|^2.0)", "nikic/php-parser": "Required to use PhpParser. (^4.0)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\CodeParser\\": "src/"}}, "license": ["MIT"], "description": "A code parser component for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["code-parser", "hyperf", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/codec", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/codec.git", "reference": "effc71c25e2d53c00fcf41da8bca083ac8a0db0e"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/codec/v3.1.42/hyperf-codec-v3.1.42.zip", "reference": "effc71c25e2d53c00fcf41da8bca083ac8a0db0e", "shasum": ""}, "require": {"ext-json": "*", "ext-xml": "*", "hyperf/contract": "~3.1.0", "php": ">=8.1"}, "suggest": {"ext-igbinary": "Required to use IgbinarySerializerPacker."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Codec\\": "src/"}}, "license": ["MIT"], "description": "A codec component for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["codec", "hyperf", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/collection", "version": "v3.1.50", "source": {"type": "git", "url": "https://github.com/hyperf/collection.git", "reference": "9ec6c151c6e1ce8407d617b7813eb52f4fb3c363"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/collection/v3.1.50/hyperf-collection-v3.1.50.zip", "reference": "9ec6c151c6e1ce8407d617b7813eb52f4fb3c363", "shasum": ""}, "require": {"hyperf/conditionable": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/macroable": "~3.1.0", "hyperf/stringable": "~3.1.0", "php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Collection\\": "src/"}}, "license": ["MIT"], "description": "Hyperf Collection package which come from illuminate/collections", "homepage": "https://hyperf.io", "keywords": ["collection", "hyperf", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2025-01-08T06:57:58+00:00"}, {"name": "hyperf/command", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/command.git", "reference": "43047270c15bce06e19d217dc5ba02b284830e25"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/command/v3.1.42/hyperf-command-v3.1.42.zip", "reference": "43047270c15bce06e19d217dc5ba02b284830e25", "shasum": ""}, "require": {"hyperf/collection": "~3.1.0", "hyperf/context": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/coroutine": "~3.1.0", "hyperf/di": "~3.1.0", "hyperf/stringable": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/tappable": "~3.1.0", "php": ">=8.1", "psr/event-dispatcher": "^1.0", "symfony/console": "^5.0 || ^6.0 || ^7.0"}, "suggest": {"hyperf/di": "Required to use annotations.", "hyperf/event": "Required to use listeners."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Command\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Command\\": "src/"}}, "license": ["MIT"], "description": "Command for hyperf", "keywords": ["command", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/conditionable", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/conditionable.git", "reference": "dec9dec9dbde14e20f3d7ba000c3302381019de1"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/conditionable/v3.1.42/hyperf-conditionable-v3.1.42.zip", "reference": "dec9dec9dbde14e20f3d7ba000c3302381019de1", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Conditionable\\": "src/"}}, "license": ["MIT"], "description": "Hyperf Macroable package which come from illuminate/conditionable", "homepage": "https://hyperf.io", "keywords": ["conditionable", "hyperf", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/config", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/config.git", "reference": "1df5e310aab752d6195f89f5cc98daf3cdc4bb6e"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/config/v3.1.42/hyperf-config-v3.1.42.zip", "reference": "1df5e310aab752d6195f89f5cc98daf3cdc4bb6e", "shasum": ""}, "require": {"hyperf/collection": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/support": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "symfony/finder": "^5.0 || ^6.0 || ^7.0"}, "suggest": {"hyperf/context": "Required to use config()", "hyperf/di": "Allows using @Value annotation", "hyperf/event": "Allows using @Value annotation", "hyperf/framework": "Allows using @Value annotation", "vlucas/phpdotenv": "Allows using enviroment value to override the config"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Config\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["./src/Functions.php"], "psr-4": {"Hyperf\\Config\\": "src/"}}, "license": ["MIT"], "description": "An independent component that provides configuration container.", "homepage": "https://hyperf.io", "keywords": ["config", "configuration", "hyperf", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/constants", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/constants.git", "reference": "e1e1184779cd163f9603ce234e1ecccb6fe382ae"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/constants/v3.1.42/hyperf-constants-v3.1.42.zip", "reference": "e1e1184779cd163f9603ce234e1ecccb6fe382ae", "shasum": ""}, "require": {"hyperf/di": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1"}, "suggest": {"hyperf/translation": "Required to use translation."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Constants\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Constants\\": "src/"}}, "license": ["MIT"], "description": "A constants component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["constants", "hyperf", "php"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/context", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/context.git", "reference": "ac666862d644db7d813342c880826a1fda599bdf"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/context/v3.1.42/hyperf-context-v3.1.42.zip", "reference": "ac666862d644db7d813342c880826a1fda599bdf", "shasum": ""}, "require": {"hyperf/engine": "^2.0", "php": ">=8.1"}, "suggest": {"swow/psr7-plus": "Required to use RequestContext and ResponseContext"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Context\\": "src/"}}, "license": ["MIT"], "description": "A coroutine/application context library.", "homepage": "https://hyperf.io", "keywords": ["Context", "hyperf", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/contract", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/contract.git", "reference": "6ef2c7f98917c52ccda3a37ae65b190848dde6c4"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/contract/v3.1.42/hyperf-contract-v3.1.42.zip", "reference": "6ef2c7f98917c52ccda3a37ae65b190848dde6c4", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Contract\\": "src/"}}, "license": ["MIT"], "description": "The contracts of Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/coordinator", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/coordinator.git", "reference": "a0497d2a260f166ab53fed2eca6bb4e48b49ef56"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/coordinator/v3.1.42/hyperf-coordinator-v3.1.42.zip", "reference": "a0497d2a260f166ab53fed2eca6bb4e48b49ef56", "shasum": ""}, "require": {"hyperf/engine": "^2.0", "php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Coordinator\\": "src/"}}, "license": ["MIT"], "description": "Hyperf Coordinator", "homepage": "https://hyperf.io", "keywords": ["Coordinator", "hyperf", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/coroutine", "version": "v3.1.50", "source": {"type": "git", "url": "https://github.com/hyperf/coroutine.git", "reference": "c353b3fbd86e30b5b51219e8867d479ea11e6811"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/coroutine/v3.1.50/hyperf-coroutine-v3.1.50.zip", "reference": "c353b3fbd86e30b5b51219e8867d479ea11e6811", "shasum": ""}, "require": {"hyperf/context": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/engine": "^2.0", "php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Coroutine\\": "src/"}}, "license": ["MIT"], "description": "Hyperf Coroutine", "homepage": "https://hyperf.io", "keywords": ["coroutine", "hyperf", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-12-24T08:59:48+00:00"}, {"name": "hyperf/crontab", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/crontab.git", "reference": "be1187515aabbfe96b2f6a5330b4ddd742e971c7"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/crontab/v3.1.42/hyperf-crontab-v3.1.42.zip", "reference": "be1187515aabbfe96b2f6a5330b4ddd742e971c7", "shasum": ""}, "require": {"hyperf/conditionable": "~3.1.0", "hyperf/framework": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/tappable": "~3.1.0", "hyperf/utils": "~3.1.0", "nesbot/carbon": "^2.0", "php": ">=8.1"}, "suggest": {"hyperf/command": "Required to use command trigger.", "hyperf/process": "Auto register the Crontab process for server."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Crontab\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Crontab\\": "src/"}}, "license": ["MIT"], "description": "A crontab component for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["crontab", "hyperf", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/database", "version": "v3.1.48", "source": {"type": "git", "url": "https://github.com/hyperf/database.git", "reference": "a16b070ee2ac2ec580a4c6f5bb6243350bed69e6"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/database/v3.1.48/hyperf-database-v3.1.48.zip", "reference": "a16b070ee2ac2ec580a4c6f5bb6243350bed69e6", "shasum": ""}, "require": {"hyperf/code-parser": "~3.1.0", "hyperf/collection": "~3.1.23", "hyperf/conditionable": "~3.1.0", "hyperf/macroable": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/tappable": "~3.1.0", "hyperf/utils": "~3.1.0", "nesbot/carbon": "^2.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/event-dispatcher": "^1.0"}, "suggest": {"doctrine/dbal": "Required to rename columns (^3.0).", "hyperf/paginator": "Required to paginate the result set (~3.1.0).", "nikic/php-parser": "Required to use ModelCommand. (^4.0)", "php-di/phpdoc-reader": "Required to use ModelCommand. (^2.2)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Database\\": "src/"}}, "license": ["MIT"], "description": "A flexible database library.", "homepage": "https://hyperf.io", "keywords": ["database", "hyperf", "php"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-12-12T01:58:16+00:00"}, {"name": "hyperf/db-connection", "version": "v3.1.44", "source": {"type": "git", "url": "https://github.com/hyperf/db-connection.git", "reference": "95dbb713fda5556106b803d0201e1631645985b5"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/db-connection/v3.1.44/hyperf-db-connection-v3.1.44.zip", "reference": "95dbb713fda5556106b803d0201e1631645985b5", "shasum": ""}, "require": {"hyperf/database": "~3.1.0", "hyperf/di": "~3.1.0", "hyperf/framework": "~3.1.0", "hyperf/model-listener": "~3.1.0", "hyperf/pool": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\DbConnection\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\DbConnection\\": "src/"}}, "license": ["MIT"], "description": "A hyperf db connection handler for hyperf/database.", "homepage": "https://hyperf.io", "keywords": ["Connection", "database", "hyperf", "php"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-10-11T08:58:16+00:00"}, {"name": "hyperf/di", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/di.git", "reference": "72b65de5022e3dca79ae1902c058048b9519aa72"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/di/v3.1.42/hyperf-di-v3.1.42.zip", "reference": "72b65de5022e3dca79ae1902c058048b9519aa72", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0", "hyperf/code-parser": "~3.1.0", "hyperf/pipeline": "~3.1.0", "hyperf/stdlib": "~3.1.0", "hyperf/support": "~3.1.0", "nikic/php-parser": "^4.1", "php": ">=8.1", "php-di/phpdoc-reader": "^2.2", "psr/container": "^1.0 || ^2.0", "symfony/finder": "^5.0 || ^6.0 || ^7.0", "vlucas/phpdotenv": "^5.0"}, "suggest": {"ext-pcntl": "Required to scan annotations.", "hyperf/config": "Require this component for annotation scan progress to retrieve the scan path."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Di\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Di\\": "src/"}}, "license": ["MIT"], "description": "A DI for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["annotation", "di", "hyperf", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/dispatcher", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/dispatcher.git", "reference": "5cbdfd586bb8c3bbbabed5a23cec7faf52b744b0"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/dispatcher/v3.1.42/hyperf-dispatcher-v3.1.42.zip", "reference": "5cbdfd586bb8c3bbbabed5a23cec7faf52b744b0", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/http-message": "^1.0 || ^2.0", "psr/http-server-middleware": "^1.0"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Dispatcher\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Dispatcher\\": "src/"}}, "license": ["MIT"], "description": "A HTTP Server for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["dispatcher", "filter", "hyperf", "middleware", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/engine", "version": "v2.12.1", "source": {"type": "git", "url": "https://github.com/hyperf/engine.git", "reference": "90be8143841482dcd00051050986251e126c6132"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/engine/v2.12.1/hyperf-engine-v2.12.1.zip", "reference": "90be8143841482dcd00051050986251e126c6132", "shasum": ""}, "require": {"hyperf/engine-contract": "~1.11.0", "php": ">=8.0"}, "conflict": {"ext-swoole": "<5.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "hyperf/guzzle": "^3.0", "hyperf/http-message": "^3.0", "mockery/mockery": "^1.5", "phpstan/phpstan": "^1.0", "phpunit/phpunit": "^9.4", "swoole/ide-helper": "5.*"}, "suggest": {"ext-sockets": "*", "ext-swoole": ">=5.0", "hyperf/http-message": "Required to use ResponseEmitter.", "psr/http-message": "Required to use WebSocket Frame."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Engine\\ConfigProvider"}, "branch-alias": {"dev-master": "2.12-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Engine\\": "src/"}}, "license": ["MIT"], "description": "Coroutine engine provided by swoole.", "keywords": ["engine", "hyperf", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-12-17T12:36:21+00:00"}, {"name": "hyperf/engine-contract", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/hyperf/engine-contract.git", "reference": "d478052ed1c5304eef7be68aae6cf42392611a15"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/engine-contract/v1.11.0/hyperf-engine-contract-v1.11.0.zip", "reference": "d478052ed1c5304eef7be68aae6cf42392611a15", "shasum": ""}, "require": {"php": ">=8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "mockery/mockery": "^1.0", "phpstan/phpstan": "^1.0", "phpunit/phpunit": ">=7.0", "psr/http-message": "^1.0", "swoole/ide-helper": "^4.5"}, "suggest": {"psr/http-message": "Required to use WebSocket Frame."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"Hyperf\\Engine\\Contract\\": "src/"}}, "license": ["MIT"], "description": "Contract for Coroutine Engine", "keywords": ["contract", "coroutine", "engine", "hyperf", "php"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-11-19T04:15:31+00:00"}, {"name": "hyperf/event", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/event.git", "reference": "2b5fbbc94674a1a5e1622896eb3f7ecc80aa38c4"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/event/v3.1.42/hyperf-event-v3.1.42.zip", "reference": "2b5fbbc94674a1a5e1622896eb3f7ecc80aa38c4", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/stdlib": "~3.1.0", "php": ">=8.1", "psr/event-dispatcher": "^1.0"}, "suggest": {"hyperf/di": "Required to use annotatioins."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Event\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Event\\": "src/"}}, "license": ["MIT"], "description": "an event manager that implements PSR-14.", "homepage": "https://hyperf.io", "keywords": ["event", "hyperf", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/exception-handler", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/exception-handler.git", "reference": "df2135fb0ffe0bb61032911038aea6488077cdef"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/exception-handler/v3.1.42/hyperf-exception-handler-v3.1.42.zip", "reference": "df2135fb0ffe0bb61032911038aea6488077cdef", "shasum": ""}, "require": {"hyperf/context": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/dispatcher": "~3.1.0", "hyperf/http-message": "~3.1.0", "hyperf/stdlib": "~3.1.0", "hyperf/support": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/http-message": "^1.0 || ^2.0", "swow/psr7-plus": "^1.0"}, "suggest": {"hyperf/di": "Required to use #[ExceptionHandler]", "hyperf/event": "Required to use listeners", "hyperf/framework": "Required to use listeners", "hyperf/stringable": "Required to use WhoopsExceptionHandler"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\ExceptionHandler\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\ExceptionHandler\\": "src/"}}, "license": ["MIT"], "description": "Exception handler for hyperf", "homepage": "https://hyperf.io", "keywords": ["exception-handler", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/filesystem", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/filesystem.git", "reference": "dd3ecec490554620c0298ac9bea2d81964686b01"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/filesystem/v3.1.42/hyperf-filesystem-v3.1.42.zip", "reference": "dd3ecec490554620c0298ac9bea2d81964686b01", "shasum": ""}, "require": {"hyperf/di": "~3.1.0", "league/flysystem": "^1.0 || ^2.0 || ^3.0", "php": ">=8.1"}, "suggest": {"hyperf/flysystem-oss": "Required to use aliyun oss adapter when use `league/flysystem` v2.0", "hyperf/guzzle": "required to use s3 adapter", "league/flysystem-aws-s3-v3": "required to use s3 adapter", "league/flysystem-memory": "required to use memory adapter", "overtrue/flysystem-cos": "Required to use cos adapter (^3.0|^4.0)", "overtrue/flysystem-qiniu": "Required to use qiniu adapter (^1.0|^2.0)", "xxtime/flysystem-aliyun-oss": "Required to use aliyun oss adapter when use `league/flysystem` v1.0"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Filesystem\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Filesystem\\": "src/"}}, "license": ["MIT"], "description": "flysystem integration for hyperf", "keywords": ["hyperf", "php"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/framework", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/framework.git", "reference": "7b317d3891698a1eb0308e7306730d2ada1d6ff4"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/framework/v3.1.42/hyperf-framework-v3.1.42.zip", "reference": "7b317d3891698a1eb0308e7306730d2ada1d6ff4", "shasum": ""}, "require": {"fig/http-message-util": "^1.1.2", "hyperf/contract": "~3.1.0", "hyperf/coordinator": "~3.1.0", "hyperf/coroutine": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/event-dispatcher": "^1.0", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "suggest": {"ext-swoole": "Required to use swoole engine.", "hyperf/command": "Required to use Command annotation.", "hyperf/di": "Required to use Command annotation.", "hyperf/dispatcher": "Required to use BootApplication event.", "symfony/event-dispatcher": "Required to use symfony event dispatcher (^5.0|^6.0)."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Framework\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Framework\\": "src/"}}, "license": ["MIT"], "description": "A coroutine framework that focuses on hyperspeed and flexible, specifically use for build microservices and middlewares.", "homepage": "https://hyperf.io", "keywords": ["Microservice", "framework", "hyperf", "middleware", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/guzzle", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/guzzle.git", "reference": "fe838557530bf7b2d39dc604563c3a3ff8d5618f"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/guzzle/v3.1.42/hyperf-guzzle-v3.1.42.zip", "reference": "fe838557530bf7b2d39dc604563c3a3ff8d5618f", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.3 || ^7.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/http-message": "^1.0 || ^2.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "hyperf/pool": "Required to use pool handler."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Guzzle\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Guzzle\\": "src/"}}, "license": ["MIT"], "description": "Swoole coroutine handler for guzzle", "keywords": ["Guzzle", "handler", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/http-message", "version": "v3.1.48", "source": {"type": "git", "url": "https://github.com/hyperf/http-message.git", "reference": "534ce81af0feaa0c4a9e132af1c6a9c5527a8d85"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/http-message/v3.1.48/hyperf-http-message-v3.1.48.zip", "reference": "534ce81af0feaa0c4a9e132af1c6a9c5527a8d85", "shasum": ""}, "require": {"hyperf/codec": "~3.1.0", "hyperf/engine": "^2.11", "hyperf/support": "~3.1.0", "laminas/laminas-mime": "^2.7", "php": ">=8.1", "psr/http-message": "^1.0 || ^2.0", "swow/psr7-plus": "^1.0"}, "suggest": {"psr/container": "Required to replace RequestParserInterface."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\HttpMessage\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\HttpMessage\\": "src/"}}, "license": ["MIT"], "description": "microservice framework base on swoole", "keywords": ["http-message", "hyperf", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-12-05T02:41:08+00:00"}, {"name": "hyperf/http-server", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/http-server.git", "reference": "4727f15a743c6e9ca0a6b3c8494c5c62bae82f5a"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/http-server/v3.1.42/hyperf-http-server-v3.1.42.zip", "reference": "4727f15a743c6e9ca0a6b3c8494c5c62bae82f5a", "shasum": ""}, "require": {"hyperf/codec": "~3.1.0", "hyperf/collection": "~3.1.0", "hyperf/context": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/coroutine": "~3.1.0", "hyperf/dispatcher": "~3.1.0", "hyperf/event": "~3.1.0", "hyperf/exception-handler": "~3.1.0", "hyperf/http-message": "~3.1.0", "hyperf/macroable": "~3.1.0", "hyperf/serializer": "~3.1.0", "hyperf/server": "~3.1.0", "hyperf/stdlib": "~3.1.0", "hyperf/support": "~3.1.0", "nikic/fast-route": "^1.3", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "swow/psr7-plus": "^1.0"}, "suggest": {"hyperf/di": "Required to use annotations."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\HttpServer\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\HttpServer\\": "src/"}}, "license": ["MIT"], "description": "A HTTP Server for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["http", "http-server", "hyperf", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/logger", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/logger.git", "reference": "c96d32fae44bf350ef903ebca19c91a315458d72"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/logger/v3.1.42/hyperf-logger-v3.1.42.zip", "reference": "c96d32fae44bf350ef903ebca19c91a315458d72", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "monolog/monolog": "^2.7 || ^3.1", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Logger\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Logger\\": "src/"}}, "license": ["MIT"], "description": "A logger component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "logger", "php"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/macroable", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/macroable.git", "reference": "0be650165b9e8ea073e199fac788ece70f16b6a4"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/macroable/v3.1.42/hyperf-macroable-v3.1.42.zip", "reference": "0be650165b9e8ea073e199fac788ece70f16b6a4", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Macroable\\": "src/"}}, "license": ["MIT"], "description": "Hyperf Macroable package which come from illuminate/macroable", "homepage": "https://hyperf.io", "keywords": ["hyperf", "macroable", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/memory", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/memory.git", "reference": "ccf25783d63a2610a4d797ec34c1e0093b755da2"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/memory/v3.1.42/hyperf-memory-v3.1.42.zip", "reference": "ccf25783d63a2610a4d797ec34c1e0093b755da2", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Memory\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Memory\\": "src/"}}, "license": ["MIT"], "description": "An independent component that use to operate and manage memory.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "memory", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/model-cache", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/model-cache.git", "reference": "f1af97e63d12f9e2149bdef478541689cd31775f"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/model-cache/v3.1.42/hyperf-model-cache-v3.1.42.zip", "reference": "f1af97e63d12f9e2149bdef478541689cd31775f", "shasum": ""}, "require": {"hyperf/codec": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/db-connection": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0"}, "suggest": {"hyperf/event": "Required to use DeleteCacheListener."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\ModelCache\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\ModelCache\\": "src/"}}, "license": ["MIT"], "description": "A model cache component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "model-cache", "php"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/model-listener", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/model-listener.git", "reference": "0181882fb6034cf2eac81b84b5c65c187af9f3a4"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/model-listener/v3.1.42/hyperf-model-listener-v3.1.42.zip", "reference": "0181882fb6034cf2eac81b84b5c65c187af9f3a4", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/database": "~3.1.0", "hyperf/di": "~3.1.0", "hyperf/event": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\ModelListener\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\ModelListener\\": "src/"}}, "license": ["MIT"], "description": "A model listener for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "model-listener", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/pipeline", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/pipeline.git", "reference": "096d9a9f87ddea33209f134d30ae8d8867a195c7"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/pipeline/v3.1.42/hyperf-pipeline-v3.1.42.zip", "reference": "096d9a9f87ddea33209f134d30ae8d8867a195c7", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Pipeline\\": "src/"}}, "license": ["MIT"], "description": "Hyperf Macroable package which come from illuminate/pipeline", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "pipeline", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/pool", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/pool.git", "reference": "004dd811bf760ea0032913a31284102742abb737"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/pool/v3.1.42/hyperf-pool-v3.1.42.zip", "reference": "004dd811bf760ea0032913a31284102742abb737", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0"}, "suggest": {"psr/event-dispatcher": "Required to use events."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Pool\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Pool\\": "src/"}}, "license": ["MIT"], "description": "An independent universal connection pool component.", "homepage": "https://hyperf.io", "keywords": ["connection-pool", "hyperf", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/process", "version": "v3.1.48", "source": {"type": "git", "url": "https://github.com/hyperf/process.git", "reference": "8d68398bdb4f2623af1bec846399b6ce29bd7d2c"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/process/v3.1.48/hyperf-process-v3.1.48.zip", "reference": "8d68398bdb4f2623af1bec846399b6ce29bd7d2c", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/event-dispatcher": "^1.0"}, "suggest": {"hyperf/di": "Required to use annotations.", "hyperf/event": "Required to dump the message before and after process.", "hyperf/framework": "Required to use BootProcessListener."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Process\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Process\\": "src/"}}, "license": ["MIT"], "description": "A process component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "process"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-12-02T10:54:30+00:00"}, {"name": "hyperf/redis", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/redis.git", "reference": "973a92c34be60353e978d85c434e65f366a817dd"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/redis/v3.1.42/hyperf-redis-v3.1.42.zip", "reference": "973a92c34be60353e978d85c434e65f366a817dd", "shasum": ""}, "require": {"ext-redis": "^5.0 || ^6.0", "hyperf/contract": "~3.1.0", "hyperf/pool": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/tappable": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0"}, "suggest": {"ext-redis": "Required to use sentinel mode (>=5.2).", "hyperf/di": "Create the RedisPool via dependency injection."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Redis\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Redis\\": "src/"}}, "license": ["MIT"], "description": "A redis component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "pool", "redis"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/serializer", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/serializer.git", "reference": "03c8a4840e0a7be83670c2fb0f850a2204fad076"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/serializer/v3.1.42/hyperf-serializer-v3.1.42.zip", "reference": "03c8a4840e0a7be83670c2fb0f850a2204fad076", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "php": ">=8.1"}, "suggest": {"hyperf/di": "Required to use ExceptionNormalizer", "symfony/property-access": "Required to use SymfonyNormalizer (^5.0|^6.0)", "symfony/serializer": "Required to use SymfonyNormalizer (^5.0|^6.0)"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Serializer\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Serializer\\": "src/"}}, "license": ["MIT"], "description": "A serializer component for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "swoole", "tappable"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/server", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/server.git", "reference": "e10c5ce6d9b72d3ca9ad16d36977e2e64d975460"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/server/v3.1.42/hyperf-server-v3.1.42.zip", "reference": "e10c5ce6d9b72d3ca9ad16d36977e2e64d975460", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/coordinator": "~3.1.0", "hyperf/engine": "^2.8", "hyperf/support": "~3.1.0", "hyperf/tappable": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/event-dispatcher": "^1.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "symfony/console": "^5.0 || ^6.0 || ^7.0"}, "suggest": {"hyperf/event": "Dump the info after server start.", "hyperf/framework": "Dump the info after server start."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Server\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Server\\": "src/"}}, "license": ["MIT"], "description": "A base server library for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "server", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/stdlib", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/stdlib.git", "reference": "13393734a4cc6c9878390b1f6b0fc7e5202c6b59"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/stdlib/v3.1.42/hyperf-stdlib-v3.1.42.zip", "reference": "13393734a4cc6c9878390b1f6b0fc7e5202c6b59", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Stdlib\\": "src/"}}, "license": ["MIT"], "description": "A stdlib component for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "stdlib", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/stringable", "version": "v3.1.50", "source": {"type": "git", "url": "https://github.com/hyperf/stringable.git", "reference": "89ab60e9ccabf024f5afc81a72f630cc67ae6687"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/stringable/v3.1.50/hyperf-stringable-v3.1.50.zip", "reference": "89ab60e9ccabf024f5afc81a72f630cc67ae6687", "shasum": ""}, "require": {"ext-mbstring": "*", "hyperf/collection": "~3.1.0", "hyperf/conditionable": "~3.1.0", "hyperf/macroable": "~3.1.0", "hyperf/tappable": "~3.1.0", "php": ">=8.1"}, "suggest": {"doctrine/inflector": "Required to use plural and singular methods.(^2.0|^3.0)", "ramsey/uuid": "Required to use uuid and orderedUuid methods.(^4.7|^5.0)", "symfony/uid": "Required to use ulid method.(^5.0|^6.0)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Stringable\\": "src/"}}, "license": ["MIT"], "description": "Hyperf Stringable package which come from illuminate/support", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "stringable", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2025-01-08T06:57:58+00:00"}, {"name": "hyperf/support", "version": "v3.1.50", "source": {"type": "git", "url": "https://github.com/hyperf/support.git", "reference": "899b7dbfe39b60d25ec71da3ad445d9db0bbce1a"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/support/v3.1.50/hyperf-support-v3.1.50.zip", "reference": "899b7dbfe39b60d25ec71da3ad445d9db0bbce1a", "shasum": ""}, "require": {"hyperf/collection": "~3.1.0", "hyperf/context": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/coroutine": "~3.1.0", "hyperf/macroable": "~3.1.0", "hyperf/stringable": "~3.1.0", "php": ">=8.1"}, "suggest": {"nesbot/carbon": "Use Carbon as DateTime object.(^2.0)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Support\\": "src/"}}, "license": ["MIT"], "description": "A support component for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "support", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2025-01-08T07:28:13+00:00"}, {"name": "hyperf/tappable", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/tappable.git", "reference": "f5c5d343c95238dcb3fe500876ceadc175e221f8"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/tappable/v3.1.42/hyperf-tappable-v3.1.42.zip", "reference": "f5c5d343c95238dcb3fe500876ceadc175e221f8", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Tappable\\": "src/"}}, "license": ["MIT"], "description": "Hyperf Macroable package which come from illuminate/tappable", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "swoole", "tappable"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/tracer", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/tracer.git", "reference": "8ec4b4f2499592a3d748e3760aa63bca31c78883"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/tracer/v3.1.42/hyperf-tracer-v3.1.42.zip", "reference": "8ec4b4f2499592a3d748e3760aa63bca31c78883", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/di": "~3.1.0", "hyperf/guzzle": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "jcchavezs/zipkin-opentracing": "^2.0", "opentracing/opentracing": "^1.0", "php": ">=8.1", "psr/http-message": "^1.0 || ^2.0"}, "suggest": {"hyperf/event": "Required to use DbQueryExecutedListener.", "jonahgeorge/jaeger-client-php": "Required (^0.6) to use jaeger tracing.", "longlang/phpkafka": "Required (^1.2) to use Kafka Producer."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Tracer\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Tracer\\": "src/"}}, "license": ["MIT"], "description": "A open tracing system implemented for Hyperf or other coroutine framework", "homepage": "https://hyperf.io", "keywords": ["hyperf", "open-tracing", "php", "zipkin"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/translation", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/translation.git", "reference": "0bca5490a99b0ea5200d5753fd096338ec24c25f"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/translation/v3.1.42/hyperf-translation-v3.1.42.zip", "reference": "0bca5490a99b0ea5200d5753fd096338ec24c25f", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/macroable": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Translation\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Translation\\": "src/"}}, "license": ["MIT"], "description": "An independent translation component, forked by illuminate/translation.", "keywords": ["hyperf", "translation"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/utils", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/utils.git", "reference": "4b13a567a61d08a3c4d058499e28a5b26fc18f1c"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/utils/v3.1.42/hyperf-utils-v3.1.42.zip", "reference": "4b13a567a61d08a3c4d058499e28a5b26fc18f1c", "shasum": ""}, "require": {"doctrine/inflector": "^2.0", "hyperf/code-parser": "~3.1.0", "hyperf/codec": "~3.1.0", "hyperf/collection": "~3.1.0", "hyperf/context": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/coordinator": "~3.1.0", "hyperf/coroutine": "~3.1.0", "hyperf/engine": "^2.0", "hyperf/macroable": "~3.1.0", "hyperf/serializer": "~3.1.0", "hyperf/stringable": "~3.1.0", "hyperf/support": "~3.1.0", "php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "license": ["MIT"], "description": "A tools package that could help developer solved the problem quickly.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "swoole", "utils"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/validation", "version": "v3.1.47", "source": {"type": "git", "url": "https://github.com/hyperf/validation.git", "reference": "a3d39fc5e0940f17e3b137c9136464916754f073"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/validation/v3.1.47/hyperf-validation-v3.1.47.zip", "reference": "a3d39fc5e0940f17e3b137c9136464916754f073", "shasum": ""}, "require": {"egulias/email-validator": "^3.0", "hyperf/collection": "~3.1.0", "hyperf/conditionable": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/di": "~3.1.0", "hyperf/framework": "~3.1.0", "hyperf/macroable": "~3.1.0", "hyperf/stringable": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/tappable": "~3.1.0", "hyperf/translation": "~3.1.0", "hyperf/utils": "~3.1.0", "nesbot/carbon": "^2.21", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/event-dispatcher": "^1.0", "psr/http-message": "^1.0 || ^2.0"}, "suggest": {"hyperf/database": "Required if you want to use the database validation rule (~3.1.0).", "hyperf/http-server": "Required if you want to use the request validation rule (~3.1.0)."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Validation\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Validation\\": "src/"}}, "license": ["MIT"], "description": "hyperf validation", "keywords": ["hyperf", "validation"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-11-28T01:51:55+00:00"}, {"name": "hyperf/view", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/view.git", "reference": "bc8b1eb4378b7f61a9d6f478d6f0b84dc4734dc0"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/view/v3.1.42/hyperf-view-v3.1.42.zip", "reference": "bc8b1eb4378b7f61a9d6f478d6f0b84dc4734dc0", "shasum": ""}, "require": {"hyperf/context": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "swow/psr7-plus": "^1.0"}, "suggest": {"duncan3dc/blade": "Required to use blade as a view render engine.", "hyperf/task": "Required to use task as a view render mode.", "league/plates": "Required to use plates as a view render engine.", "smarty/smarty": "Required to use smarty as a view render engine.", "twig/twig": "Required to use twig as a view render engine."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\View\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\View\\": "src/"}}, "license": ["MIT"], "description": "A view library for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "swoole", "view"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/view-engine", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/view-engine.git", "reference": "a92dc3e5d089889e02029297e02fc2e0515c11a7"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/view-engine/v3.1.42/hyperf-view-engine-v3.1.42.zip", "reference": "a92dc3e5d089889e02029297e02fc2e0515c11a7", "shasum": ""}, "require": {"ext-json": "*", "hyperf/collection": "~3.1.23", "hyperf/config": "~3.1.0", "hyperf/di": "~3.1.0", "hyperf/event": "~3.1.0", "hyperf/macroable": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/tappable": "~3.1.0", "hyperf/utils": "~3.1.0", "hyperf/view": "~3.1.0", "php": ">=8.1"}, "suggest": {"hyperf/session": "Required to use ShareErrorsFromSession.", "hyperf/validation": "Required to use ShareErrorsFromSession and ValidationExceptionHandle."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\ViewEngine\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\ViewEngine\\": "src/"}}, "license": ["MIT"], "keywords": ["engine", "hyperf", "php", "view"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "illuminate/bus", "version": "v11.41.3", "source": {"type": "git", "url": "https://github.com/illuminate/bus.git", "reference": "ed6dd9b36ff18a57a951bd5946f1c3a534f900cb"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/illuminate/bus/v11.41.3/illuminate-bus-v11.41.3.zip", "reference": "ed6dd9b36ff18a57a951bd5946f1c3a534f900cb", "shasum": ""}, "require": {"illuminate/collections": "^11.0", "illuminate/contracts": "^11.0", "illuminate/pipeline": "^11.0", "illuminate/support": "^11.0", "php": "^8.2"}, "suggest": {"illuminate/queue": "Required to use closures when chaining jobs (^7.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Bus\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Bus package.", "homepage": "https://laravel.com", "time": "2025-01-22T21:19:28+00:00"}, {"name": "illuminate/collections", "version": "v11.41.3", "source": {"type": "git", "url": "https://github.com/illuminate/collections.git", "reference": "80c85f81573cc4c024da05312119f9149a6b64c1"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/illuminate/collections/v11.41.3/illuminate-collections-v11.41.3.zip", "reference": "80c85f81573cc4c024da05312119f9149a6b64c1", "shasum": ""}, "require": {"illuminate/conditionable": "^11.0", "illuminate/contracts": "^11.0", "illuminate/macroable": "^11.0", "php": "^8.2"}, "suggest": {"symfony/var-dumper": "Required to use the dump method (^7.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "autoload": {"files": ["functions.php", "helpers.php"], "psr-4": {"Illuminate\\Support\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Collections package.", "homepage": "https://laravel.com", "time": "2025-01-24T15:40:32+00:00"}, {"name": "illuminate/conditionable", "version": "v11.41.3", "source": {"type": "git", "url": "https://github.com/illuminate/conditionable.git", "reference": "911df1bda950a3b799cf80671764e34eede131c6"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/illuminate/conditionable/v11.41.3/illuminate-conditionable-v11.41.3.zip", "reference": "911df1bda950a3b799cf80671764e34eede131c6", "shasum": ""}, "require": {"php": "^8.0.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Support\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Conditionable package.", "homepage": "https://laravel.com", "time": "2024-11-21T16:28:56+00:00"}, {"name": "illuminate/container", "version": "v11.41.3", "source": {"type": "git", "url": "https://github.com/illuminate/container.git", "reference": "1caf7d6cf42078fa8d30f26f1e43943ed6b01dae"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/illuminate/container/v11.41.3/illuminate-container-v11.41.3.zip", "reference": "1caf7d6cf42078fa8d30f26f1e43943ed6b01dae", "shasum": ""}, "require": {"illuminate/contracts": "^11.0", "php": "^8.2", "psr/container": "^1.1.1|^2.0.1"}, "provide": {"psr/container-implementation": "1.1|2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Container\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Container package.", "homepage": "https://laravel.com", "time": "2025-01-28T20:44:34+00:00"}, {"name": "illuminate/contracts", "version": "v11.41.3", "source": {"type": "git", "url": "https://github.com/illuminate/contracts.git", "reference": "534b697fc1dd9fbdd9fbf2f33fc9dcbb943dea75"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/illuminate/contracts/v11.41.3/illuminate-contracts-v11.41.3.zip", "reference": "534b697fc1dd9fbdd9fbf2f33fc9dcbb943dea75", "shasum": ""}, "require": {"php": "^8.2", "psr/container": "^1.1.1|^2.0.1", "psr/simple-cache": "^1.0|^2.0|^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Contracts\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Contracts package.", "homepage": "https://laravel.com", "time": "2025-01-10T20:57:00+00:00"}, {"name": "illuminate/events", "version": "v11.41.3", "source": {"type": "git", "url": "https://github.com/illuminate/events.git", "reference": "2fcff2a924d1e2d58561f7b5d5078d6847a9eee8"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/illuminate/events/v11.41.3/illuminate-events-v11.41.3.zip", "reference": "2fcff2a924d1e2d58561f7b5d5078d6847a9eee8", "shasum": ""}, "require": {"illuminate/bus": "^11.0", "illuminate/collections": "^11.0", "illuminate/container": "^11.0", "illuminate/contracts": "^11.0", "illuminate/macroable": "^11.0", "illuminate/support": "^11.0", "php": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "autoload": {"files": ["functions.php"], "psr-4": {"Illuminate\\Events\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Events package.", "homepage": "https://laravel.com", "time": "2024-12-06T19:16:00+00:00"}, {"name": "illuminate/filesystem", "version": "v11.41.3", "source": {"type": "git", "url": "https://github.com/illuminate/filesystem.git", "reference": "a8768cca697ddf6f0cecc6f5bd4763808d84c0b7"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/illuminate/filesystem/v11.41.3/illuminate-filesystem-v11.41.3.zip", "reference": "a8768cca697ddf6f0cecc6f5bd4763808d84c0b7", "shasum": ""}, "require": {"illuminate/collections": "^11.0", "illuminate/contracts": "^11.0", "illuminate/macroable": "^11.0", "illuminate/support": "^11.0", "php": "^8.2", "symfony/finder": "^7.0.3"}, "suggest": {"ext-fileinfo": "Required to use the Filesystem class.", "ext-ftp": "Required to use the Flysystem FTP driver.", "ext-hash": "Required to use the Filesystem class.", "illuminate/http": "Required for handling uploaded files (^7.0).", "league/flysystem": "Required to use the Flysystem local driver (^3.25.1).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (^3.25.1).", "league/flysystem-ftp": "Required to use the Flysystem FTP driver (^3.25.1).", "league/flysystem-sftp-v3": "Required to use the Flysystem SFTP driver (^3.25.1).", "psr/http-message": "Required to allow Storage::put to accept a StreamInterface (^1.0).", "symfony/filesystem": "Required to enable support for relative symbolic links (^7.0).", "symfony/mime": "Required to enable support for guessing extensions (^7.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "autoload": {"files": ["functions.php"], "psr-4": {"Illuminate\\Filesystem\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Filesystem package.", "homepage": "https://laravel.com", "time": "2025-01-24T16:08:55+00:00"}, {"name": "illuminate/macroable", "version": "v11.41.3", "source": {"type": "git", "url": "https://github.com/illuminate/macroable.git", "reference": "e1cb9e51b9ed5d3c9bc1ab431d0a52fe42a990ed"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/illuminate/macroable/v11.41.3/illuminate-macroable-v11.41.3.zip", "reference": "e1cb9e51b9ed5d3c9bc1ab431d0a52fe42a990ed", "shasum": ""}, "require": {"php": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Support\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Macroable package.", "homepage": "https://laravel.com", "time": "2024-06-28T20:10:30+00:00"}, {"name": "illuminate/pipeline", "version": "v11.41.3", "source": {"type": "git", "url": "https://github.com/illuminate/pipeline.git", "reference": "a784f85ca9d6c37435c542ea487d78206a7df3ad"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/illuminate/pipeline/v11.41.3/illuminate-pipeline-v11.41.3.zip", "reference": "a784f85ca9d6c37435c542ea487d78206a7df3ad", "shasum": ""}, "require": {"illuminate/contracts": "^11.0", "illuminate/support": "^11.0", "php": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Pipeline\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Pipeline package.", "homepage": "https://laravel.com", "time": "2025-01-07T23:29:34+00:00"}, {"name": "illuminate/support", "version": "v11.41.3", "source": {"type": "git", "url": "https://github.com/illuminate/support.git", "reference": "5dc4a31f34d8a0529cf1fd6b7fe167f6cae91e0c"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/illuminate/support/v11.41.3/illuminate-support-v11.41.3.zip", "reference": "5dc4a31f34d8a0529cf1fd6b7fe167f6cae91e0c", "shasum": ""}, "require": {"doctrine/inflector": "^2.0", "ext-ctype": "*", "ext-filter": "*", "ext-mbstring": "*", "illuminate/collections": "^11.0", "illuminate/conditionable": "^11.0", "illuminate/contracts": "^11.0", "illuminate/macroable": "^11.0", "nesbot/carbon": "^2.72.6|^3.8.4", "php": "^8.2", "voku/portable-ascii": "^2.0.2"}, "conflict": {"tightenco/collect": "<5.5.33"}, "replace": {"spatie/once": "*"}, "suggest": {"illuminate/filesystem": "Required to use the Composer class (^11.0).", "laravel/serializable-closure": "Required to use the once function (^1.3|^2.0).", "league/commonmark": "Required to use Str::markdown() and Stringable::markdown() (^2.6).", "league/uri": "Required to use the Uri class (^7.5.1).", "ramsey/uuid": "Required to use Str::uuid() (^4.7).", "symfony/process": "Required to use the Composer class (^7.0).", "symfony/uid": "Required to use Str::ulid() (^7.0).", "symfony/var-dumper": "Required to use the dd function (^7.0).", "vlucas/phpdotenv": "Required to use the Env class and env helper (^5.6.1)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "autoload": {"files": ["functions.php", "helpers.php"], "psr-4": {"Illuminate\\Support\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Support package.", "homepage": "https://laravel.com", "time": "2025-01-30T09:11:36+00:00"}, {"name": "illuminate/view", "version": "v11.41.3", "source": {"type": "git", "url": "https://github.com/illuminate/view.git", "reference": "9f9bed5b55b1b888a6149860b4b26b20ca4435bf"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/illuminate/view/v11.41.3/illuminate-view-v11.41.3.zip", "reference": "9f9bed5b55b1b888a6149860b4b26b20ca4435bf", "shasum": ""}, "require": {"ext-tokenizer": "*", "illuminate/collections": "^11.0", "illuminate/container": "^11.0", "illuminate/contracts": "^11.0", "illuminate/events": "^11.0", "illuminate/filesystem": "^11.0", "illuminate/macroable": "^11.0", "illuminate/support": "^11.0", "php": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\View\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate View package.", "homepage": "https://laravel.com", "time": "2025-01-22T21:19:28+00:00"}, {"name": "jcchavezs/zipkin-opentracing", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/jcchavezs/zipkin-php-opentracing.git", "reference": "6bd908f84ff611dff4d64c5e7d510bd6a1107575"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/jcchavezs/zipkin-opentracing/2.0.4/jcchavezs-zipkin-opentracing-2.0.4.zip", "reference": "6bd908f84ff611dff4d64c5e7d510bd6a1107575", "shasum": ""}, "require": {"opentracing/opentracing": "^1.0.1", "openzipkin/zipkin": "^3.0.0", "php": ">=7.4 || ^8.0"}, "provide": {"opentracing/opentracing": "1.0.0"}, "require-dev": {"phpspec/prophecy-phpunit": "^2.0", "phpstan/phpstan": "^0.12.26", "phpunit/phpunit": "^9.0", "squizlabs/php_codesniffer": "3.*"}, "type": "library", "autoload": {"psr-4": {"ZipkinOpenTracing\\": "./src/ZipkinOpenTracing/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A Zipkin bridge with OpenTracing", "funding": [{"url": "https://www.paypal.me/jcchavezs", "type": "paypal"}], "time": "2023-11-03T22:38:29+00:00"}, {"name": "laminas/laminas-mime", "version": "2.12.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-mime.git", "reference": "08cc544778829b7d68d27a097885bd6e7130135e"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/laminas/laminas-mime/2.12.0/laminas-laminas-mime-2.12.0.zip", "reference": "08cc544778829b7d68d27a097885bd6e7130135e", "shasum": ""}, "require": {"laminas/laminas-stdlib": "^2.7 || ^3.0", "php": "~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0"}, "conflict": {"zendframework/zend-mime": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.4.0", "laminas/laminas-mail": "^2.19.0", "phpunit/phpunit": "~9.5.25"}, "suggest": {"laminas/laminas-mail": "Laminas\\Mail component"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Mime\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "description": "Create and parse MIME messages and parts", "homepage": "https://laminas.dev", "keywords": ["laminas", "mime"], "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "abandoned": "symfony/mime", "time": "2023-11-02T16:47:19+00:00"}, {"name": "laminas/laminas-stdlib", "version": "3.20.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-stdlib.git", "reference": "8974a1213be42c3e2f70b2c27b17f910291ab2f4"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/laminas/laminas-stdlib/3.20.0/laminas-laminas-stdlib-3.20.0.zip", "reference": "8974a1213be42c3e2f70b2c27b17f910291ab2f4", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"zendframework/zend-stdlib": "*"}, "require-dev": {"laminas/laminas-coding-standard": "^3.0", "phpbench/phpbench": "^1.3.1", "phpunit/phpunit": "^10.5.38", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.26.1"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Stdlib\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "description": "SPL extensions, array utilities, error handlers, and more", "homepage": "https://laminas.dev", "keywords": ["laminas", "stdlib"], "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-10-29T13:46:07+00:00"}, {"name": "league/flysystem", "version": "3.29.1", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "edc1bb7c86fab0776c3287dbd19b5fa278347319"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/league/flysystem/3.29.1/league-flysystem-3.29.1.zip", "reference": "edc1bb7c86fab0776c3287dbd19b5fa278347319", "shasum": ""}, "require": {"league/flysystem-local": "^3.0.0", "league/mime-type-detection": "^1.0.0", "php": "^8.0.2"}, "conflict": {"async-aws/core": "<1.19.0", "async-aws/s3": "<1.14.0", "aws/aws-sdk-php": "3.209.31 || 3.210.0", "guzzlehttp/guzzle": "<7.0", "guzzlehttp/ringphp": "<1.1.1", "phpseclib/phpseclib": "3.0.15", "symfony/http-client": "<5.2"}, "require-dev": {"async-aws/s3": "^1.5 || ^2.0", "async-aws/simple-s3": "^1.1 || ^2.0", "aws/aws-sdk-php": "^3.295.10", "composer/semver": "^3.0", "ext-fileinfo": "*", "ext-ftp": "*", "ext-mongodb": "^1.3", "ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.5", "google/cloud-storage": "^1.23", "guzzlehttp/psr7": "^2.6", "microsoft/azure-storage-blob": "^1.1", "mongodb/mongodb": "^1.2", "phpseclib/phpseclib": "^3.0.36", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.5.11|^10.0", "sabre/dav": "^4.6.0"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "File storage abstraction for PHP", "keywords": ["WebDAV", "aws", "cloud", "file", "files", "filesystem", "filesystems", "ftp", "s3", "sftp", "storage"], "time": "2024-10-08T08:58:34+00:00"}, {"name": "league/flysystem-local", "version": "3.29.0", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem-local.git", "reference": "e0e8d52ce4b2ed154148453d321e97c8e931bd27"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/league/flysystem-local/3.29.0/league-flysystem-local-3.29.0.zip", "reference": "e0e8d52ce4b2ed154148453d321e97c8e931bd27", "shasum": ""}, "require": {"ext-fileinfo": "*", "league/flysystem": "^3.0.0", "league/mime-type-detection": "^1.0.0", "php": "^8.0.2"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\Local\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Local filesystem adapter for Flysystem.", "keywords": ["Flysystem", "file", "files", "filesystem", "local"], "time": "2024-08-09T21:24:39+00:00"}, {"name": "league/mime-type-detection", "version": "1.16.0", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "2d6702ff215bf922936ccc1ad31007edc76451b9"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/league/mime-type-detection/1.16.0/league-mime-type-detection-1.16.0.zip", "reference": "2d6702ff215bf922936ccc1ad31007edc76451b9", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3 || ^10.0"}, "type": "library", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2024-09-21T08:32:55+00:00"}, {"name": "monolog/monolog", "version": "3.8.1", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "aef6ee73a77a66e404dd6540934a9ef1b3c855b4"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/monolog/monolog/3.8.1/monolog-monolog-3.8.1.zip", "reference": "aef6ee73a77a66e404dd6540934a9ef1b3c855b4", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.8", "phpstan/phpstan": "^2", "phpstan/phpstan-deprecation-rules": "^2", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "^10.5.17 || ^11.0.7", "predis/predis": "^1.1 || ^2", "rollbar/rollbar": "^4.0", "ruflin/elastica": "^7 || ^8", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2024-12-05T17:15:07+00:00"}, {"name": "nesbot/carbon", "version": "2.72.6", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon.git", "reference": "1e9d50601e7035a4c61441a208cb5bed73e108c5"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/nesbot/carbon/2.72.6/nesbot-carbon-2.72.6.zip", "reference": "1e9d50601e7035a4c61441a208cb5bed73e108c5", "shasum": ""}, "require": {"carbonphp/carbon-doctrine-types": "*", "ext-json": "*", "php": "^7.1.8 || ^8.0", "psr/clock": "^1.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"doctrine/dbal": "^2.0 || ^3.1.4 || ^4.0", "doctrine/orm": "^2.7 || ^3.0", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "ondrejmirtes/better-reflection": "<6", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.99 || ^1.7.14", "phpunit/php-file-iterator": "^2.0.5 || ^3.0.6", "phpunit/phpunit": "^7.5.20 || ^8.5.26 || ^9.5.20", "squizlabs/php_codesniffer": "^3.4"}, "bin": ["bin/carbon"], "type": "library", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-2.x": "2.x-dev", "dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "time": "2024-12-27T09:28:11+00:00"}, {"name": "nikic/fast-route", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/nikic/FastRoute.git", "reference": "181d480e08d9476e61381e04a71b34dc0432e812"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/nikic/fast-route/v1.3.0/nikic-fast-route-v1.3.0.zip", "reference": "181d480e08d9476e61381e04a71b34dc0432e812", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35|~5.7"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"FastRoute\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Fast request router for PHP", "keywords": ["router", "routing"], "time": "2018-02-13T20:26:39+00:00"}, {"name": "nikic/php-parser", "version": "v4.19.4", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "715f4d25e225bc47b293a8b997fe6ce99bf987d2"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/nikic/php-parser/v4.19.4/nikic-php-parser-v4.19.4.zip", "reference": "715f4d25e225bc47b293a8b997fe6ce99bf987d2", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.1"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "time": "2024-09-29T15:01:53+00:00"}, {"name": "opentracing/opentracing", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/opentracing/opentracing-php.git", "reference": "cd60bd1fb2a25280600bc74c7f9e0c13881a9116"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/opentracing/opentracing/1.0.2/opentracing-opentracing-1.0.2.zip", "reference": "cd60bd1fb2a25280600bc74c7f9e0c13881a9116", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"phpstan/phpstan": "~0.12", "phpunit/phpunit": "^7.0 || ^9.0", "squizlabs/php_codesniffer": "3.*"}, "type": "library", "autoload": {"files": ["src/OpenTracing/Tags.php", "src/OpenTracing/Formats.php"], "psr-4": {"OpenTracing\\": "src/OpenTracing/"}}, "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "OpenTracing API for PHP", "time": "2022-01-27T19:59:21+00:00"}, {"name": "openzipkin/zipkin", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/openzipkin/zipkin-php.git", "reference": "e2809f8b6775796d2116b3ca73576a1734296ff6"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/openzipkin/zipkin/3.2.0/openzipkin-zipkin-3.2.0.zip", "reference": "e2809f8b6775796d2116b3ca73576a1734296ff6", "shasum": ""}, "require": {"ext-curl": "*", "php": "^7.4 || ^8.0", "psr/http-message": "~1.0 || ~2.0", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"ext-mysqli": "*", "jcchavezs/httptest": "~0.2", "middlewares/fast-route": "^2.0", "middlewares/request-handler": "^2.0", "nyholm/psr7": "^1.4", "phpspec/prophecy-phpunit": "^2.0", "phpstan/phpstan": "^0.12.26", "phpunit/phpunit": "~9", "psr/http-client": "^1.0", "psr/http-server-middleware": "^1.0", "squizlabs/php_codesniffer": "3.*"}, "suggest": {"ext-mysqli": "Allows to use mysqli instrumentation.", "psr/http-client": "Allows to instrument HTTP clients following PSR18.", "psr/http-server-middleware": "Allows to instrument HTTP servers via middlewares following PSR15."}, "type": "library", "autoload": {"files": ["./src/Zipkin/Propagation/Id.php", "./src/Zipkin/Timestamp.php", "./src/Zipkin/Kind.php", "./src/Zipkin/Tags.php", "./src/Zipkin/Annotations.php", "./src/Zipkin/SpanName.php"], "psr-4": {"Zipkin\\": "./src/<PERSON>ip<PERSON>/"}}, "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A Zipkin instrumentation for PHP", "homepage": "https://github.com/openzipkin/zipkin-php", "keywords": ["distributed-tracing", "openzipkin", "tracing", "zipkin"], "time": "2023-09-28T20:54:04+00:00"}, {"name": "paragonie/constant_time_encoding", "version": "v3.0.0", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "df1e7fde177501eee2037dd159cf04f5f301a512"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/paragonie/constant_time_encoding/v3.0.0/paragonie-constant_time_encoding-v3.0.0.zip", "reference": "df1e7fde177501eee2037dd159cf04f5f301a512", "shasum": ""}, "require": {"php": "^8"}, "require-dev": {"phpunit/phpunit": "^9", "vimeo/psalm": "^4|^5"}, "type": "library", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "time": "2024-05-08T12:36:18+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/paragonie/random_compat/v9.99.100/paragonie-random_compat-v9.99.100.zip", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "time": "2020-10-15T08:29:30+00:00"}, {"name": "php-amqplib/php-amqplib", "version": "v3.7.2", "source": {"type": "git", "url": "https://github.com/php-amqplib/php-amqplib.git", "reference": "738a73eb0019b6c99d9bc25d7a0c0dd8f56a5199"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/php-amqplib/php-amqplib/v3.7.2/php-amqplib-php-amqplib-v3.7.2.zip", "reference": "738a73eb0019b6c99d9bc25d7a0c0dd8f56a5199", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-sockets": "*", "php": "^7.2||^8.0", "phpseclib/phpseclib": "^2.0|^3.0"}, "conflict": {"php": "7.4.0 - 7.4.1"}, "replace": {"videlalvaro/php-amqplib": "self.version"}, "require-dev": {"ext-curl": "*", "nategood/httpful": "^0.2.20", "phpunit/phpunit": "^7.5|^9.5", "squizlabs/php_codesniffer": "^3.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"PhpAmqpLib\\": "PhpAmqpLib/"}}, "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "Original Maintainer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Maintainer"}], "description": "Formerly videlalvaro/php-amqplib.  This library is a pure PHP implementation of the AMQP protocol. It's been tested against RabbitMQ.", "homepage": "https://github.com/php-amqplib/php-amqplib/", "keywords": ["message", "queue", "rabbitmq"], "time": "2024-11-21T09:21:41+00:00"}, {"name": "php-di/phpdoc-reader", "version": "2.2.1", "source": {"type": "git", "url": "https://github.com/PHP-DI/PhpDocReader.git", "reference": "66daff34cbd2627740ffec9469ffbac9f8c8185c"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/php-di/phpdoc-reader/2.2.1/php-di-phpdoc-reader-2.2.1.zip", "reference": "66daff34cbd2627740ffec9469ffbac9f8c8185c", "shasum": ""}, "require": {"php": ">=7.2.0"}, "require-dev": {"mnapoli/hard-mode": "~0.3.0", "phpunit/phpunit": "^8.5|^9.0"}, "type": "library", "autoload": {"psr-4": {"PhpDocReader\\": "src/PhpDocReader"}}, "license": ["MIT"], "description": "PhpDocReader parses @var and @param values in PHP docblocks (supports namespaced class names with the same resolution rules as PHP)", "keywords": ["phpdoc", "reflection"], "time": "2020-10-12T12:39:22+00:00"}, {"name": "phpoption/phpoption", "version": "1.9.3", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/phpoption/phpoption/1.9.3/phpoption-phpoption-1.9.3.zip", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption", "type": "tidelift"}], "time": "2024-07-20T21:41:07+00:00"}, {"name": "phpseclib/phpseclib", "version": "3.0.43", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "709ec107af3cb2f385b9617be72af8cf62441d02"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/phpseclib/phpseclib/3.0.43/phpseclib-phpseclib-3.0.43.zip", "reference": "709ec107af3cb2f385b9617be72af8cf62441d02", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1|^2|^3", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": ">=5.6.1"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-dom": "Install the DOM extension to load XML formatted public keys.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib3\\": "phpseclib/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2024-12-14T21:12:59+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/psr/clock/1.0.0/psr-clock-1.0.0.zip", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/psr/container/2.0.2/psr-container-2.0.2.zip", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/psr/event-dispatcher/1.0.0/psr-event-dispatcher-1.0.0.zip", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/psr/http-client/1.0.3/psr-http-client-1.0.3.zip", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/psr/http-factory/1.1.0/psr-http-factory-1.1.0.zip", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/psr/http-message/2.0/psr-http-message-2.0.zip", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/http-server-handler", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-handler.git", "reference": "84c4fb66179be4caaf8e97bd239203245302e7d4"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/psr/http-server-handler/1.0.2/psr-http-server-handler-1.0.2.zip", "reference": "84c4fb66179be4caaf8e97bd239203245302e7d4", "shasum": ""}, "require": {"php": ">=7.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP server-side request handler", "keywords": ["handler", "http", "http-interop", "psr", "psr-15", "psr-7", "request", "response", "server"], "time": "2023-04-10T20:06:20+00:00"}, {"name": "psr/http-server-middleware", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-middleware.git", "reference": "c1481f747daaa6a0782775cd6a8c26a1bf4a3829"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/psr/http-server-middleware/1.0.2/psr-http-server-middleware-1.0.2.zip", "reference": "c1481f747daaa6a0782775cd6a8c26a1bf4a3829", "shasum": ""}, "require": {"php": ">=7.0", "psr/http-message": "^1.0 || ^2.0", "psr/http-server-handler": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP server-side middleware", "keywords": ["http", "http-interop", "middleware", "psr", "psr-15", "psr-7", "request", "response"], "time": "2023-04-11T06:14:47+00:00"}, {"name": "psr/log", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/psr/log/3.0.2/psr-log-3.0.2.zip", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2024-09-11T13:17:53+00:00"}, {"name": "psr/simple-cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/psr/simple-cache/3.0.0/psr-simple-cache-3.0.0.zip", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "time": "2021-10-29T13:26:27+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/ralouphie/getallheaders/3.0.3/ralouphie-getallheaders-3.0.3.zip", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/collection", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/ramsey/collection.git", "reference": "a4b48764bfbb8f3a6a4d1aeb1a35bb5e9ecac4a5"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/ramsey/collection/2.0.0/ramsey-collection-2.0.0.zip", "reference": "a4b48764bfbb8f3a6a4d1aeb1a35bb5e9ecac4a5", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"captainhook/plugin-composer": "^5.3", "ergebnis/composer-normalize": "^2.28.3", "fakerphp/faker": "^1.21", "hamcrest/hamcrest-php": "^2.0", "jangregor/phpstan-prophecy": "^1.0", "mockery/mockery": "^1.5", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpcsstandards/phpcsutils": "^1.0.0-rc1", "phpspec/prophecy-phpunit": "^2.0", "phpstan/extension-installer": "^1.2", "phpstan/phpstan": "^1.9", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.5", "psalm/plugin-mockery": "^1.1", "psalm/plugin-phpunit": "^0.18.4", "ramsey/coding-standard": "^2.0.3", "ramsey/conventional-commits": "^1.3", "vimeo/psalm": "^5.4"}, "type": "library", "extra": {"captainhook": {"force-install": true}, "ramsey/conventional-commits": {"configFile": "conventional-commits.json"}}, "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A PHP library for representing and manipulating collections.", "keywords": ["array", "collection", "hash", "map", "queue", "set"], "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/collection", "type": "tidelift"}], "time": "2022-12-31T21:50:55+00:00"}, {"name": "ramsey/uuid", "version": "4.7.6", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "91039bc1faa45ba123c4328958e620d382ec7088"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/ramsey/uuid/4.7.6/ramsey-uuid-4.7.6.zip", "reference": "91039bc1faa45ba123c4328958e620d382ec7088", "shasum": ""}, "require": {"brick/math": "^0.8.8 || ^0.9 || ^0.10 || ^0.11 || ^0.12", "ext-json": "*", "php": "^8.0", "ramsey/collection": "^1.2 || ^2.0"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"captainhook/captainhook": "^5.10", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "doctrine/annotations": "^1.8", "ergebnis/composer-normalize": "^2.15", "mockery/mockery": "^1.3", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.2", "php-mock/php-mock-mockery": "^1.3", "php-parallel-lint/php-parallel-lint": "^1.1", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^8.5 || ^9", "ramsey/composer-repl": "^1.4", "slevomat/coding-standard": "^8.4", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.9"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "extra": {"captainhook": {"force-install": true}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "license": ["MIT"], "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "keywords": ["guid", "identifier", "uuid"], "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/uuid", "type": "tidelift"}], "time": "2024-04-27T21:32:50+00:00"}, {"name": "swow/psr7-plus", "version": "v1.1.2", "source": {"type": "git", "url": "https://github.com/swow/psr7-plus.git", "reference": "7acc4924be907d2ff64edee5a2bd116620e56364"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/swow/psr7-plus/v1.1.2/swow-psr7-plus-v1.1.2.zip", "reference": "7acc4924be907d2ff64edee5a2bd116620e56364", "shasum": ""}, "require": {"php": ">=8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1|^2.0"}, "type": "library", "autoload": {"psr-4": {"Swow\\Psr7\\Message\\": "src/Message/"}}, "license": ["Apache-2.0"], "authors": [{"name": "twose", "email": "<EMAIL>"}], "description": "Modern strong-typed interfaces for Psr7, not only HTTP but also WebSocket", "keywords": ["http", "psr17", "psr7", "swow", "websocket"], "time": "2023-06-15T09:18:11+00:00"}, {"name": "symfony/console", "version": "v7.2.1", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "fefcc18c0f5d0efe3ab3152f15857298868dc2c3"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/symfony/console/v7.2.1/symfony-console-v7.2.1.zip", "reference": "fefcc18c0f5d0efe3ab3152f15857298868dc2c3", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^6.4|^7.0"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/dotenv": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/lock": "<6.4", "symfony/process": "<6.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-11T03:49:26+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/symfony/deprecation-contracts/v3.5.1/symfony-deprecation-contracts-v3.5.1.zip", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"files": ["function.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/finder", "version": "v7.2.2", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "87a71856f2f56e4100373e92529eed3171695cfb"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/symfony/finder/v7.2.2/symfony-finder-v7.2.2.zip", "reference": "87a71856f2f56e4100373e92529eed3171695cfb", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/filesystem": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-30T19:00:17+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/symfony/polyfill-ctype/v1.31.0/symfony-polyfill-ctype-v1.31.0.zip", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/symfony/polyfill-intl-grapheme/v1.31.0/symfony-polyfill-intl-grapheme-v1.31.0.zip", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/symfony/polyfill-intl-idn/v1.31.0/symfony-polyfill-intl-idn-v1.31.0.zip", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/symfony/polyfill-intl-normalizer/v1.31.0/symfony-polyfill-intl-normalizer-v1.31.0.zip", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/symfony/polyfill-mbstring/v1.31.0/symfony-polyfill-mbstring-v1.31.0.zip", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/symfony/polyfill-php80/v1.31.0/symfony-polyfill-php80-v1.31.0.zip", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/service-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/symfony/service-contracts/v3.5.1/symfony-service-contracts-v3.5.1.zip", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/string", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "446e0d146f991dde3e73f45f2c97a9faad773c82"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/symfony/string/v7.2.0/symfony-string-v7.2.0.zip", "reference": "446e0d146f991dde3e73f45f2c97a9faad773c82", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/emoji": "^7.1", "symfony/error-handler": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-13T13:31:26+00:00"}, {"name": "symfony/translation", "version": "v6.4.13", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "bee9bfabfa8b4045a66bf82520e492cddbaffa66"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/symfony/translation/v6.4.13/symfony-translation-v6.4.13.zip", "reference": "bee9bfabfa8b4045a66bf82520e492cddbaffa66", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.5|^3.0"}, "conflict": {"symfony/config": "<5.4", "symfony/console": "<5.4", "symfony/dependency-injection": "<5.4", "symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<5.4", "symfony/service-contracts": "<2.5", "symfony/twig-bundle": "<5.4", "symfony/yaml": "<5.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}, "require-dev": {"nikic/php-parser": "^4.18|^5.0", "psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/intl": "^5.4|^6.0|^7.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-27T18:14:25+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/symfony/translation-contracts/v3.5.1/symfony-translation-contracts-v3.5.1.zip", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "vlucas/phpdotenv", "version": "v5.6.1", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "a59a13791077fe3d44f90e7133eb68e7d22eaff2"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/vlucas/phpdotenv/v5.6.1/vlucas-phpdotenv-v5.6.1.zip", "reference": "a59a13791077fe3d44f90e7133eb68e7d22eaff2", "shasum": ""}, "require": {"ext-pcre": "*", "graham-campbell/result-type": "^1.1.3", "php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3", "symfony/polyfill-ctype": "^1.24", "symfony/polyfill-mbstring": "^1.24", "symfony/polyfill-php80": "^1.24"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-filter": "*", "phpunit/phpunit": "^8.5.34 || ^9.6.13 || ^10.4.2"}, "suggest": {"ext-filter": "Required to use the boolean validator."}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "5.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2024-07-20T21:52:34+00:00"}, {"name": "voku/portable-ascii", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/voku/portable-ascii.git", "reference": "b1d923f88091c6bf09699efcd7c8a1b1bfd7351d"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/voku/portable-ascii/2.0.3/voku-portable-ascii-2.0.3.zip", "reference": "b1d923f88091c6bf09699efcd7c8a1b1bfd7351d", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0 || ~9.0"}, "suggest": {"ext-intl": "Use Intl for transliterator_transliterate() support"}, "type": "library", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://www.moelleken.org/"}], "description": "Portable ASCII library - performance optimized (ascii) string functions for php.", "homepage": "https://github.com/voku/portable-ascii", "keywords": ["ascii", "clean", "php"], "funding": [{"url": "https://www.paypal.me/moelleken", "type": "custom"}, {"url": "https://github.com/voku", "type": "github"}, {"url": "https://opencollective.com/portable-ascii", "type": "open_collective"}, {"url": "https://www.patreon.com/voku", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/voku/portable-ascii", "type": "tidelift"}], "time": "2024-11-21T01:49:47+00:00"}], "packages-dev": [{"name": "clue/ndjson-react", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/clue/reactphp-ndjson.git", "reference": "392dc165fce93b5bb5c637b67e59619223c931b0"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/clue/ndjson-react/v1.3.0/clue-ndjson-react-v1.3.0.zip", "reference": "392dc165fce93b5bb5c637b67e59619223c931b0", "shasum": ""}, "require": {"php": ">=5.3", "react/stream": "^1.2"}, "require-dev": {"phpunit/phpunit": "^9.5 || ^5.7 || ^4.8.35", "react/event-loop": "^1.2"}, "type": "library", "autoload": {"psr-4": {"Clue\\React\\NDJson\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Streaming newline-delimited JSON (NDJSON) parser and encoder for ReactPHP.", "homepage": "https://github.com/clue/reactphp-ndjson", "keywords": ["NDJSON", "json", "jsonlines", "newline", "reactphp", "streaming"], "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2022-12-23T10:58:28+00:00"}, {"name": "composer/pcre", "version": "3.3.2", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/composer/pcre/3.3.2/composer-pcre-3.3.2.zip", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"phpstan/phpstan": "<1.11.10"}, "require-dev": {"phpstan/phpstan": "^1.12 || ^2", "phpstan/phpstan-strict-rules": "^1 || ^2", "phpunit/phpunit": "^8 || ^9"}, "type": "library", "extra": {"phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-11-12T16:29:46+00:00"}, {"name": "composer/semver", "version": "3.4.3", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/composer/semver/3.4.3/composer-semver-3.4.3.zip", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "symfony/phpunit-bridge": "^3 || ^7"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-09-19T14:15:21+00:00"}, {"name": "composer/xdebug-handler", "version": "3.0.5", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/composer/xdebug-handler/3.0.5/composer-xdebug-handler-3.0.5.zip", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef", "shasum": ""}, "require": {"composer/pcre": "^1 || ^2 || ^3", "php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^8.5 || ^9.6 || ^10.5"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-05-06T16:37:16+00:00"}, {"name": "evenement/evenement", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/igorw/evenement.git", "reference": "0a16b0d71ab13284339abb99d9d2bd813640efbc"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/evenement/evenement/v3.0.2/evenement-evenement-v3.0.2.zip", "reference": "0a16b0d71ab13284339abb99d9d2bd813640efbc", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^9 || ^6"}, "type": "library", "autoload": {"psr-4": {"Evenement\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}], "description": "Événement is a very simple event dispatching library for PHP", "keywords": ["event-dispatcher", "event-emitter"], "time": "2023-08-08T05:53:35+00:00"}, {"name": "fidry/cpu-core-counter", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/theofidry/cpu-core-counter.git", "reference": "8520451a140d3f46ac33042715115e290cf5785f"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/fidry/cpu-core-counter/1.2.0/fidry-cpu-core-counter-1.2.0.zip", "reference": "8520451a140d3f46ac33042715115e290cf5785f", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"fidry/makefile": "^0.2.0", "fidry/php-cs-fixer-config": "^1.1.2", "phpstan/extension-installer": "^1.2.0", "phpstan/phpstan": "^1.9.2", "phpstan/phpstan-deprecation-rules": "^1.0.0", "phpstan/phpstan-phpunit": "^1.2.2", "phpstan/phpstan-strict-rules": "^1.4.4", "phpunit/phpunit": "^8.5.31 || ^9.5.26", "webmozarts/strict-phpunit": "^7.5"}, "type": "library", "autoload": {"psr-4": {"Fidry\\CpuCoreCounter\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Théo FIDRY", "email": "<EMAIL>"}], "description": "Tiny utility to get the number of CPU cores.", "keywords": ["CPU", "core"], "funding": [{"url": "https://github.com/theofidry", "type": "github"}], "time": "2024-08-06T10:04:20+00:00"}, {"name": "friendsofphp/php-cs-fixer", "version": "v3.68.5", "source": {"type": "git", "url": "https://github.com/PHP-CS-Fixer/PHP-CS-Fixer.git", "reference": "7bedb718b633355272428c60736dc97fb96daf27"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/friendsofphp/php-cs-fixer/v3.68.5/friendsofphp-php-cs-fixer-v3.68.5.zip", "reference": "7bedb718b633355272428c60736dc97fb96daf27", "shasum": ""}, "require": {"clue/ndjson-react": "^1.0", "composer/semver": "^3.4", "composer/xdebug-handler": "^3.0.3", "ext-filter": "*", "ext-json": "*", "ext-tokenizer": "*", "fidry/cpu-core-counter": "^1.2", "php": "^7.4 || ^8.0", "react/child-process": "^0.6.5", "react/event-loop": "^1.0", "react/promise": "^2.0 || ^3.0", "react/socket": "^1.0", "react/stream": "^1.0", "sebastian/diff": "^4.0 || ^5.1 || ^6.0", "symfony/console": "^5.4 || ^6.4 || ^7.0", "symfony/event-dispatcher": "^5.4 || ^6.4 || ^7.0", "symfony/filesystem": "^5.4 || ^6.4 || ^7.0", "symfony/finder": "^5.4 || ^6.4 || ^7.0", "symfony/options-resolver": "^5.4 || ^6.4 || ^7.0", "symfony/polyfill-mbstring": "^1.31", "symfony/polyfill-php80": "^1.31", "symfony/polyfill-php81": "^1.31", "symfony/process": "^5.4 || ^6.4 || ^7.2", "symfony/stopwatch": "^5.4 || ^6.4 || ^7.0"}, "require-dev": {"facile-it/paraunit": "^1.3.1 || ^2.4", "infection/infection": "^0.29.8", "justinrainbow/json-schema": "^5.3 || ^6.0", "keradus/cli-executor": "^2.1", "mikey179/vfsstream": "^1.6.12", "php-coveralls/php-coveralls": "^2.7", "php-cs-fixer/accessible-object": "^1.1", "php-cs-fixer/phpunit-constraint-isidenticalstring": "^1.5", "php-cs-fixer/phpunit-constraint-xmlmatchesxsd": "^1.5", "phpunit/phpunit": "^9.6.22 || ^10.5.40 || ^11.5.2", "symfony/var-dumper": "^5.4.48 || ^6.4.15 || ^7.2.0", "symfony/yaml": "^5.4.45 || ^6.4.13 || ^7.2.0"}, "suggest": {"ext-dom": "For handling output formats in XML", "ext-mbstring": "For handling non-UTF8 characters."}, "bin": ["php-cs-fixer"], "type": "application", "autoload": {"psr-4": {"PhpCsFixer\\": "src/"}, "exclude-from-classmap": ["src/Fixer/Internal/*"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A tool to automatically fix PHP code style", "keywords": ["Static code analysis", "fixer", "standards", "static analysis"], "funding": [{"url": "https://github.com/keradus", "type": "github"}], "time": "2025-01-30T17:00:50+00:00"}, {"name": "hamcrest/hamcrest-php", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hamcrest/hamcrest-php/v2.0.1/hamcrest-hamcrest-php-v2.0.1.zip", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "shasum": ""}, "require": {"php": "^5.3|^7.0|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "time": "2020-07-09T08:09:16+00:00"}, {"name": "hyperf/devtool", "version": "v3.1.42", "source": {"type": "git", "url": "https://github.com/hyperf/devtool.git", "reference": "ae1c8f547c21eb591a94ae3fbacf054542de82d3"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/devtool/v3.1.42/hyperf-devtool-v3.1.42.zip", "reference": "ae1c8f547c21eb591a94ae3fbacf054542de82d3", "shasum": ""}, "require": {"hyperf/code-parser": "~3.1.0", "hyperf/command": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/di": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Devtool\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Devtool\\": "src/"}}, "license": ["MIT"], "description": "A Devtool for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["dev", "devtool", "hyperf", "php", "swoole"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/testing", "version": "v3.1.48", "source": {"type": "git", "url": "https://github.com/hyperf/testing.git", "reference": "e5e5eba5c304a876dd251e774ecdb3a8ebf97edd"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/hyperf/testing/v3.1.48/hyperf-testing-v3.1.48.zip", "reference": "e5e5eba5c304a876dd251e774ecdb3a8ebf97edd", "shasum": ""}, "require": {"hyperf/codec": "~3.1.0", "hyperf/collection": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/coroutine": "~3.1.0", "hyperf/http-message": "~3.1.0", "hyperf/http-server": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "phpunit/phpunit": "^10.0", "psr/container": "^1.0 || ^2.0", "symfony/http-foundation": "^5.4 || ^6.0"}, "suggest": {"fakerphp/faker": "Required to use Faker feature.(^1.23)"}, "bin": ["co-php<PERSON>t"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Testing\\": "src/"}}, "license": ["MIT"], "description": "Testing for hyperf", "keywords": ["dev", "php", "swoole", "testing"], "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "time": "2024-12-12T02:12:29+00:00"}, {"name": "mockery/mockery", "version": "1.6.12", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "1f4efdd7d3beafe9807b08156dfcb176d18f1699"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/mockery/mockery/1.6.12/mockery-mockery-1.6.12.zip", "reference": "1f4efdd7d3beafe9807b08156dfcb176d18f1699", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": ">=7.3"}, "conflict": {"phpunit/phpunit": "<8.0"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.6.17", "symplify/easy-coding-standard": "^12.1.14"}, "type": "library", "autoload": {"files": ["library/helpers.php", "library/Mockery.php"], "psr-4": {"Mockery\\": "library/Mockery"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/padraic", "role": "Author"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://davedevelopment.co.uk", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/ghostwriter", "role": "Lead Developer"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "time": "2024-05-16T03:13:13+00:00"}, {"name": "myclabs/deep-copy", "version": "1.12.1", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "123267b2c49fbf30d78a7b2d333f6be754b94845"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/myclabs/deep-copy/1.12.1/myclabs-deep-copy-1.12.1.zip", "reference": "123267b2c49fbf30d78a7b2d333f6be754b94845", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2024-11-08T17:47:46+00:00"}, {"name": "phar-io/manifest", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "54750ef60c58e43759730615a392c31c80e23176"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/phar-io/manifest/2.0.4/phar-io-manifest-2.0.4.zip", "reference": "54750ef60c58e43759730615a392c31c80e23176", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:33:53+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/phar-io/version/3.2.1/phar-io-version-3.2.1.zip", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "time": "2022-02-21T01:04:05+00:00"}, {"name": "phpstan/phpstan", "version": "1.12.16", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "e0bb5cb78545aae631220735aa706eac633a6be9"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/phpstan/phpstan/1.12.16/phpstan-phpstan-1.12.16.zip", "reference": "e0bb5cb78545aae631220735aa706eac633a6be9", "shasum": ""}, "require": {"php": "^7.2|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "bin": ["phpstan", "phpstan.phar"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "keywords": ["dev", "static analysis"], "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}], "time": "2025-01-21T14:50:05+00:00"}, {"name": "phpunit/php-code-coverage", "version": "10.1.16", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "7e308268858ed6baedc8704a304727d20bc07c77"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/phpunit/php-code-coverage/10.1.16/phpunit-php-code-coverage-10.1.16.zip", "reference": "7e308268858ed6baedc8704a304727d20bc07c77", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.19.1 || ^5.1.0", "php": ">=8.1", "phpunit/php-file-iterator": "^4.1.0", "phpunit/php-text-template": "^3.0.1", "sebastian/code-unit-reverse-lookup": "^3.0.0", "sebastian/complexity": "^3.2.0", "sebastian/environment": "^6.1.0", "sebastian/lines-of-code": "^2.0.2", "sebastian/version": "^4.0.1", "theseer/tokenizer": "^1.2.3"}, "require-dev": {"phpunit/phpunit": "^10.1"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "type": "library", "extra": {"branch-alias": {"dev-main": "10.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-08-22T04:31:57+00:00"}, {"name": "phpunit/php-file-iterator", "version": "4.1.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "a95037b6d9e608ba092da1b23931e537cadc3c3c"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/phpunit/php-file-iterator/4.1.0/phpunit-php-file-iterator-4.1.0.zip", "reference": "a95037b6d9e608ba092da1b23931e537cadc3c3c", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-08-31T06:24:48+00:00"}, {"name": "phpunit/php-invoker", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/phpunit/php-invoker/4.0.0/phpunit-php-invoker-4.0.0.zip", "reference": "f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^10.0"}, "suggest": {"ext-pcntl": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:56:09+00:00"}, {"name": "phpunit/php-text-template", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "0c7b06ff49e3d5072f057eb1fa59258bf287a748"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/phpunit/php-text-template/3.0.1/phpunit-php-text-template-3.0.1.zip", "reference": "0c7b06ff49e3d5072f057eb1fa59258bf287a748", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-08-31T14:07:24+00:00"}, {"name": "phpunit/php-timer", "version": "6.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "e2a2d67966e740530f4a3343fe2e030ffdc1161d"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/phpunit/php-timer/6.0.0/phpunit-php-timer-6.0.0.zip", "reference": "e2a2d67966e740530f4a3343fe2e030ffdc1161d", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:57:52+00:00"}, {"name": "phpunit/phpunit", "version": "10.5.44", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "1381c62769be4bb88fa4c5aec1366c7c66ca4f36"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/phpunit/phpunit/10.5.44/phpunit-phpunit-10.5.44.zip", "reference": "1381c62769be4bb88fa4c5aec1366c7c66ca4f36", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.12.1", "phar-io/manifest": "^2.0.4", "phar-io/version": "^3.2.1", "php": ">=8.1", "phpunit/php-code-coverage": "^10.1.16", "phpunit/php-file-iterator": "^4.1.0", "phpunit/php-invoker": "^4.0.0", "phpunit/php-text-template": "^3.0.1", "phpunit/php-timer": "^6.0.0", "sebastian/cli-parser": "^2.0.1", "sebastian/code-unit": "^2.0.0", "sebastian/comparator": "^5.0.3", "sebastian/diff": "^5.1.1", "sebastian/environment": "^6.1.0", "sebastian/exporter": "^5.1.2", "sebastian/global-state": "^6.0.2", "sebastian/object-enumerator": "^5.0.0", "sebastian/recursion-context": "^5.0.0", "sebastian/type": "^4.0.0", "sebastian/version": "^4.0.1"}, "suggest": {"ext-soap": "To be able to generate mocks based on WSDL files"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-main": "10.5-dev"}}, "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/phpunit", "type": "tidelift"}], "time": "2025-01-31T07:00:38+00:00"}, {"name": "react/cache", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/reactphp/cache.git", "reference": "d47c472b64aa5608225f47965a484b75c7817d5b"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/react/cache/v1.2.0/react-cache-v1.2.0.zip", "reference": "d47c472b64aa5608225f47965a484b75c7817d5b", "shasum": ""}, "require": {"php": ">=5.3.0", "react/promise": "^3.0 || ^2.0 || ^1.1"}, "require-dev": {"phpunit/phpunit": "^9.5 || ^5.7 || ^4.8.35"}, "type": "library", "autoload": {"psr-4": {"React\\Cache\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Async, Promise-based cache interface for ReactPHP", "keywords": ["cache", "caching", "promise", "reactphp"], "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2022-11-30T15:59:55+00:00"}, {"name": "react/child-process", "version": "v0.6.6", "source": {"type": "git", "url": "https://github.com/reactphp/child-process.git", "reference": "1721e2b93d89b745664353b9cfc8f155ba8a6159"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/react/child-process/v0.6.6/react-child-process-v0.6.6.zip", "reference": "1721e2b93d89b745664353b9cfc8f155ba8a6159", "shasum": ""}, "require": {"evenement/evenement": "^3.0 || ^2.0 || ^1.0", "php": ">=5.3.0", "react/event-loop": "^1.2", "react/stream": "^1.4"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36", "react/socket": "^1.16", "sebastian/environment": "^5.0 || ^3.0 || ^2.0 || ^1.0"}, "type": "library", "autoload": {"psr-4": {"React\\ChildProcess\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Event-driven library for executing child processes with ReactPHP.", "keywords": ["event-driven", "process", "reactphp"], "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2025-01-01T16:37:48+00:00"}, {"name": "react/dns", "version": "v1.13.0", "source": {"type": "git", "url": "https://github.com/reactphp/dns.git", "reference": "eb8ae001b5a455665c89c1df97f6fb682f8fb0f5"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/react/dns/v1.13.0/react-dns-v1.13.0.zip", "reference": "eb8ae001b5a455665c89c1df97f6fb682f8fb0f5", "shasum": ""}, "require": {"php": ">=5.3.0", "react/cache": "^1.0 || ^0.6 || ^0.5", "react/event-loop": "^1.2", "react/promise": "^3.2 || ^2.7 || ^1.2.1"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36", "react/async": "^4.3 || ^3 || ^2", "react/promise-timer": "^1.11"}, "type": "library", "autoload": {"psr-4": {"React\\Dns\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Async DNS resolver for ReactPHP", "keywords": ["async", "dns", "dns-resolver", "reactphp"], "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2024-06-13T14:18:03+00:00"}, {"name": "react/event-loop", "version": "v1.5.0", "source": {"type": "git", "url": "https://github.com/reactphp/event-loop.git", "reference": "bbe0bd8c51ffc05ee43f1729087ed3bdf7d53354"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/react/event-loop/v1.5.0/react-event-loop-v1.5.0.zip", "reference": "bbe0bd8c51ffc05ee43f1729087ed3bdf7d53354", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "suggest": {"ext-pcntl": "For signal handling support when using the StreamSelectLoop"}, "type": "library", "autoload": {"psr-4": {"React\\EventLoop\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "ReactPHP's core reactor event loop that libraries can use for evented I/O.", "keywords": ["asynchronous", "event-loop"], "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2023-11-13T13:48:05+00:00"}, {"name": "react/promise", "version": "v3.2.0", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "8a164643313c71354582dc850b42b33fa12a4b63"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/react/promise/v3.2.0/react-promise-v3.2.0.zip", "reference": "8a164643313c71354582dc850b42b33fa12a4b63", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"phpstan/phpstan": "1.10.39 || 1.4.10", "phpunit/phpunit": "^9.6 || ^7.5"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2024-05-24T10:39:05+00:00"}, {"name": "react/socket", "version": "v1.16.0", "source": {"type": "git", "url": "https://github.com/reactphp/socket.git", "reference": "23e4ff33ea3e160d2d1f59a0e6050e4b0fb0eac1"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/react/socket/v1.16.0/react-socket-v1.16.0.zip", "reference": "23e4ff33ea3e160d2d1f59a0e6050e4b0fb0eac1", "shasum": ""}, "require": {"evenement/evenement": "^3.0 || ^2.0 || ^1.0", "php": ">=5.3.0", "react/dns": "^1.13", "react/event-loop": "^1.2", "react/promise": "^3.2 || ^2.6 || ^1.2.1", "react/stream": "^1.4"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36", "react/async": "^4.3 || ^3.3 || ^2", "react/promise-stream": "^1.4", "react/promise-timer": "^1.11"}, "type": "library", "autoload": {"psr-4": {"React\\Socket\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Async, streaming plaintext TCP/IP and secure TLS socket server and client connections for ReactPHP", "keywords": ["Connection", "Socket", "async", "reactphp", "stream"], "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2024-07-26T10:38:09+00:00"}, {"name": "react/stream", "version": "v1.4.0", "source": {"type": "git", "url": "https://github.com/reactphp/stream.git", "reference": "1e5b0acb8fe55143b5b426817155190eb6f5b18d"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/react/stream/v1.4.0/react-stream-v1.4.0.zip", "reference": "1e5b0acb8fe55143b5b426817155190eb6f5b18d", "shasum": ""}, "require": {"evenement/evenement": "^3.0 || ^2.0 || ^1.0", "php": ">=5.3.8", "react/event-loop": "^1.2"}, "require-dev": {"clue/stream-filter": "~1.2", "phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"psr-4": {"React\\Stream\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Event-driven readable and writable streams for non-blocking I/O in ReactPHP", "keywords": ["event-driven", "io", "non-blocking", "pipe", "reactphp", "readable", "stream", "writable"], "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2024-06-11T12:45:25+00:00"}, {"name": "sebastian/cli-parser", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "reference": "c34583b87e7b7a8055bf6c450c2c77ce32a24084"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/sebastian/cli-parser/2.0.1/sebastian-cli-parser-2.0.1.zip", "reference": "c34583b87e7b7a8055bf6c450c2c77ce32a24084", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:12:49+00:00"}, {"name": "sebastian/code-unit", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "reference": "a81fee9eef0b7a76af11d121767abc44c104e503"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/sebastian/code-unit/2.0.0/sebastian-code-unit-2.0.0.zip", "reference": "a81fee9eef0b7a76af11d121767abc44c104e503", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:58:43+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "5e3a687f7d8ae33fb362c5c0743794bbb2420a1d"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/sebastian/code-unit-reverse-lookup/3.0.0/sebastian-code-unit-reverse-lookup-3.0.0.zip", "reference": "5e3a687f7d8ae33fb362c5c0743794bbb2420a1d", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:59:15+00:00"}, {"name": "sebastian/comparator", "version": "5.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "a18251eb0b7a2dcd2f7aa3d6078b18545ef0558e"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/sebastian/comparator/5.0.3/sebastian-comparator-5.0.3.zip", "reference": "a18251eb0b7a2dcd2f7aa3d6078b18545ef0558e", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "php": ">=8.1", "sebastian/diff": "^5.0", "sebastian/exporter": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-10-18T14:56:07+00:00"}, {"name": "sebastian/complexity", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/complexity.git", "reference": "68ff824baeae169ec9f2137158ee529584553799"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/sebastian/complexity/3.2.0/sebastian-complexity-3.2.0.zip", "reference": "68ff824baeae169ec9f2137158ee529584553799", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-12-21T08:37:17+00:00"}, {"name": "sebastian/diff", "version": "5.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "c41e007b4b62af48218231d6c2275e4c9b975b2e"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/sebastian/diff/5.1.1/sebastian-diff-5.1.1.zip", "reference": "c41e007b4b62af48218231d6c2275e4c9b975b2e", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0", "symfony/process": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:15:17+00:00"}, {"name": "sebastian/environment", "version": "6.1.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "8074dbcd93529b357029f5cc5058fd3e43666984"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/sebastian/environment/6.1.0/sebastian-environment-6.1.0.zip", "reference": "8074dbcd93529b357029f5cc5058fd3e43666984", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.1-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "https://github.com/sebastian<PERSON>mann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-23T08:47:14+00:00"}, {"name": "sebastian/exporter", "version": "5.1.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "955288482d97c19a372d3f31006ab3f37da47adf"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/sebastian/exporter/5.1.2/sebastian-exporter-5.1.2.zip", "reference": "955288482d97c19a372d3f31006ab3f37da47adf", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=8.1", "sebastian/recursion-context": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:17:12+00:00"}, {"name": "sebastian/global-state", "version": "6.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "987bafff24ecc4c9ac418cab1145b96dd6e9cbd9"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/sebastian/global-state/6.0.2/sebastian-global-state-6.0.2.zip", "reference": "987bafff24ecc4c9ac418cab1145b96dd6e9cbd9", "shasum": ""}, "require": {"php": ">=8.1", "sebastian/object-reflector": "^3.0", "sebastian/recursion-context": "^5.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "https://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:19:19+00:00"}, {"name": "sebastian/lines-of-code", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "reference": "856e7f6a75a84e339195d48c556f23be2ebf75d0"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/sebastian/lines-of-code/2.0.2/sebastian-lines-of-code-2.0.2.zip", "reference": "856e7f6a75a84e339195d48c556f23be2ebf75d0", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-12-21T08:38:20+00:00"}, {"name": "sebastian/object-enumerator", "version": "5.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "202d0e344a580d7f7d04b3fafce6933e59dae906"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/sebastian/object-enumerator/5.0.0/sebastian-object-enumerator-5.0.0.zip", "reference": "202d0e344a580d7f7d04b3fafce6933e59dae906", "shasum": ""}, "require": {"php": ">=8.1", "sebastian/object-reflector": "^3.0", "sebastian/recursion-context": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:08:32+00:00"}, {"name": "sebastian/object-reflector", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "24ed13d98130f0e7122df55d06c5c4942a577957"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/sebastian/object-reflector/3.0.0/sebastian-object-reflector-3.0.0.zip", "reference": "24ed13d98130f0e7122df55d06c5c4942a577957", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:06:18+00:00"}, {"name": "sebastian/recursion-context", "version": "5.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "05909fb5bc7df4c52992396d0116aed689f93712"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/sebastian/recursion-context/5.0.0/sebastian-recursion-context-5.0.0.zip", "reference": "05909fb5bc7df4c52992396d0116aed689f93712", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:05:40+00:00"}, {"name": "sebastian/type", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "462699a16464c3944eefc02ebdd77882bd3925bf"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/sebastian/type/4.0.0/sebastian-type-4.0.0.zip", "reference": "462699a16464c3944eefc02ebdd77882bd3925bf", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:10:45+00:00"}, {"name": "sebastian/version", "version": "4.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "c51fa83a5d8f43f1402e3f32a005e6262244ef17"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/sebastian/version/4.0.1/sebastian-version-4.0.1.zip", "reference": "c51fa83a5d8f43f1402e3f32a005e6262244ef17", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-07T11:34:05+00:00"}, {"name": "swoole/ide-helper", "version": "5.1.6", "source": {"type": "git", "url": "https://github.com/swoole/ide-helper.git", "reference": "c5149a01c00e4ed56fc7b3ffeb6823e69acb4a76"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/swoole/ide-helper/5.1.6/swoole-ide-helper-5.1.6.zip", "reference": "c5149a01c00e4ed56fc7b3ffeb6823e69acb4a76", "shasum": ""}, "type": "library", "license": ["Apache-2.0"], "authors": [{"name": "Team Swoole", "email": "<EMAIL>"}], "description": "IDE help files for Swoole.", "time": "2024-11-29T07:21:36+00:00"}, {"name": "symfony/event-dispatcher", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "910c5db85a5356d0fea57680defec4e99eb9c8c1"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/symfony/event-dispatcher/v7.2.0/symfony-event-dispatcher-v7.2.0.zip", "reference": "910c5db85a5356d0fea57680defec4e99eb9c8c1", "shasum": ""}, "require": {"php": ">=8.2", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/error-handler": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/symfony/event-dispatcher-contracts/v3.5.1/symfony-event-dispatcher-contracts-v3.5.1.zip", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/filesystem", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "b8dce482de9d7c9fe2891155035a7248ab5c7fdb"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/symfony/filesystem/v7.2.0/symfony-filesystem-v7.2.0.zip", "reference": "b8dce482de9d7c9fe2891155035a7248ab5c7fdb", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "require-dev": {"symfony/process": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-25T15:15:23+00:00"}, {"name": "symfony/http-foundation", "version": "v6.4.18", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "d0492d6217e5ab48f51fca76f64cf8e78919d0db"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/symfony/http-foundation/v6.4.18/symfony-http-foundation-v6.4.18.zip", "reference": "d0492d6217e5ab48f51fca76f64cf8e78919d0db", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php83": "^1.27"}, "conflict": {"symfony/cache": "<6.4.12|>=7.0,<7.1.5"}, "require-dev": {"doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "symfony/cache": "^6.4.12|^7.1.5", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4.12|^6.0.12|^6.1.4|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/rate-limiter": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-09T15:48:56+00:00"}, {"name": "symfony/options-resolver", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "7da8fbac9dcfef75ffc212235d76b2754ce0cf50"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/symfony/options-resolver/v7.2.0/symfony-options-resolver-v7.2.0.zip", "reference": "7da8fbac9dcfef75ffc212235d76b2754ce0cf50", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-20T11:17:29+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/symfony/polyfill-php81/v1.31.0/symfony-polyfill-php81-v1.31.0.zip", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php83", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/symfony/polyfill-php83/v1.31.0/symfony-polyfill-php83-v1.31.0.zip", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/process", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "d34b22ba9390ec19d2dd966c40aa9e8462f27a7e"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/symfony/process/v7.2.0/symfony-process-v7.2.0.zip", "reference": "d34b22ba9390ec19d2dd966c40aa9e8462f27a7e", "shasum": ""}, "require": {"php": ">=8.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-06T14:24:19+00:00"}, {"name": "symfony/stopwatch", "version": "v7.2.2", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "e46690d5b9d7164a6d061cab1e8d46141b9f49df"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/symfony/stopwatch/v7.2.2/symfony-stopwatch-v7.2.2.zip", "reference": "e46690d5b9d7164a6d061cab1e8d46141b9f49df", "shasum": ""}, "require": {"php": ">=8.2", "symfony/service-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a way to profile code", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-18T14:28:33+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/theseer/tokenizer/1.2.3/theseer-tokenizer-1.2.3.zip", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:36:25+00:00"}], "aliases": [], "minimum-stability": "dev", "stability-flags": {}, "prefer-stable": true, "prefer-lowest": false, "platform": {"php": ">=8.1"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}