<?php

declare(strict_types=1);
/**
 * Routes Configuration
 *
 * This file defines all routes for the application
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

use Hyperf\HttpServer\Router\Router;

// Default route
Router::addRoute(['GET', 'POST', 'HEAD'], '/', 'App\Controller\IndexController@index');

Router::get('/favicon.ico', function () {
    return '';
});

# 后台
Router::addGroup('/admin', function () {

});

# 平台
Router::addGroup('/platform', function () {

});

// API Routes
Router::addGroup('/api', function () {
    // API Version 1

    Router::addGroup('/ly-hospital', function () {
        Router::addGroup('/v1', function () {
            # test
//            Router::get('/test', 'App\Controller\His\V1\HisController@test');
            /**
             * ============================== HIS  接口 =============================
             * @doc /doc/微信公众号his接口文档v4.pdf
             */
            # 获取科室列表
            Router::get('/department-list', [\App\Controller\His\V1\HisController::class, 'getHospitalDepartmentList']);
            # 获取科室医生列表
            Router::get('/department-doctor', [\App\Controller\His\V1\HisController::class, 'getDoctorListByDepartment']);
            # 获取医生排班列表
            Router::get('/doctor-scheduling-list', [\App\Controller\His\V1\HisController::class, 'getDoctorSchedulingList']);
            # 保存就诊人
            Router::post('/patient-save', [\App\Controller\His\V1\HisController::class, 'patientSave']);
            # 查询就诊人
            Router::get('/patient-info', [\App\Controller\His\V1\HisController::class, 'patientQuery']);
            # 设置默认就诊人
            Router::put('/patient-default', [\App\Controller\His\V1\HisController::class, 'patientSetDefault']);
            # 解绑就诊人
            Router::put('/patient-unbind', [\App\Controller\His\V1\HisController::class, 'patientUnbind']);
            # 就诊人列表
            Router::get('/patient-list', [\App\Controller\His\V1\HisController::class, 'patientList']);
            # 预约挂号
            Router::post('/appointment-registration', [\App\Controller\His\V1\HisController::class, 'appointmentRegistration']);
            # 取消预约
            Router::put('/registration-cancel', [\App\Controller\His\V1\HisController::class, 'registrationCancel']);
            # 挂号退号
            Router::put('/registration-refund', [\App\Controller\His\V1\HisController::class, 'registrationRefund']);
            # 挂号结算
            Router::post('/registration-pay', [\App\Controller\His\V1\HisController::class, 'registrationPay']);
            # 挂号列表
            Router::get('/registration-list', [\App\Controller\His\V1\HisController::class, 'registrationList']);
            # 检查医生头像
            Router::get('/check-image/{filename}', [\App\Controller\His\V1\HisController::class, 'checkImageExists']);
            # 门诊缴费列表
            Router::get('/outpatient-payment', [\App\Controller\His\V1\HisController::class, 'outpatientPayment']);
            # 门诊缴费结算
            Router::post('/patient-pay', [\App\Controller\His\V1\HisController::class, 'patientPay']);
            # 缴费记录
            Router::get('/payment-list', [\App\Controller\His\V1\HisController::class, 'paymentList']);
            # 处方列表
            Router::get('/prescription-list', [\App\Controller\His\V1\HisController::class, 'prescriptionList']);
            # 就诊人编号及证件号互查
            Router::get('/patient-card-number', [\App\Controller\His\V1\HisController::class, 'patientCardNumber']);
            # 全部就诊人列表
            Router::get('/all-patient-list', [\App\Controller\His\V1\HisController::class, 'getHisAllPatientList']);
            # 就诊记录
            Router::get('/attendance-records', [\App\Controller\His\V1\HisController::class, 'getAttendanceRecords']);
            # 病历查询
            Router::get('/medical-record', [\App\Controller\His\V1\HisController::class, 'getMedicalRecord']);
            # 38.查询病人已作废结算记录
            Router::get('/invalid-settlement-list', [\App\Controller\His\V1\HisController::class, 'getInvalidSettlementList']);

            /**
             * ============================== 舌诊 2.0 接口 =============================
             * @link https://market.aliyun.com/apimarket/detail/cmapi00066970#sku=yuncode6097000002
             * @doc /doc/舌面诊断2.0api.pdf
             */

            # ⾆⾯诊断
            Router::post('/face-diagnosis', [\App\Controller\Diag\V1\DiagnosisController::class, 'faceDiagnosis']);
            # 获取报告
            Router::post('/face-diagnosis-report', [\App\Controller\Diag\V1\DiagnosisController::class, 'faceDiagnosisReport']);

            /**
             * ============================== AI =============================
             */
            Router::addGroup('/bot', function () {
                Router::post('/chat/completions', [\App\Controller\Ai\V1\AiBotController::class, 'chat']);
            });

            /**
             * ============================== 量表 =============================
             */
            # 获取量表问题列表
            Router::get('/scales-question', [\App\Controller\Scales\V1\ScaleController::class, 'scalesQuestion']);
            # 创建量表报告获取任务
            Router::post('/scales-report-task', [\App\Controller\Scales\V1\ScaleController::class, 'scalesReportTask']);
            # 获取报告状态
            Router::get('/scales-report-status', [\App\Controller\Scales\V1\ScaleController::class, 'scalesReportStatus']);
        });
    }, ['middleware' => [\App\Middleware\ApiAuthMiddleware::class]]);

    // You can add more API versions here
    // Example:
    // Router::addGroup('/v2', function () {
    //     // V2 API routes
    // });
});

// Add a route for static files
Router::get('/doctor/avatar/{filename:.+}', function ($request, $filename) {
    $filePath = BASE_PATH . '/public/doctor/avatar/' . $filename;
    if (file_exists($filePath)) {
        return file_get_contents($filePath);
    }
    return '';
});

Router::get('/department/picture/{filename:.+}', function ($request, $filename) {
    $filePath = BASE_PATH . '/public/department/picture/' . $filename;
    if (file_exists($filePath)) {
        return file_get_contents($filePath);
    }
    return '';
});

Router::get('/diag/picture/{filename:.+}', function ($request, $filename) {
    $filePath = BASE_PATH . '/public/uploads/diag/202505/' . $filename;
    if (file_exists($filePath)) {
        return file_get_contents($filePath);
    }
    return '';
});

// 日志查看器路由
Router::get('/logs', 'App\Controller\LogViewerController@index');
Router::get('/logs/view', 'App\Controller\LogViewerController@view');
