<?php

declare(strict_types=1);
/**
 * Middleware Configuration
 *
 * This file defines global middleware for the application
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
return [
    'http' => [
        // Global HTTP middleware
        // Note: API authentication is handled by the ApiAuthMiddleware in the controller
        \Hyperf\Validation\Middleware\ValidationMiddleware::class
    ],
];
