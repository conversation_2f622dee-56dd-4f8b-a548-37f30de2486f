### 六一医院中间平台API 服务

> 模拟调用签名生成

```javascript
const CryptoJS = require('crypto-js');

var ak = '******';
var sk = '******';
var ts = Math.floor(Date.now() / 1000);
var nonce = generateRandomString();
pm.environment.set("ak", ak);
pm.environment.set("timestamp", ts);
pm.environment.set("nonce", nonce);

// 加密
// var dataStr = ak + sk + ts + nonce;
// const encryptedMessage = sha256Encrypt(dataStr);
var arr = [ts, nonce, ak];
arr.sort((a, b) => a.localeCompare(b)); 
var dataStr = arr.join('');
const encryptedMessage = sha256Encrypt(dataStr, sk);
console.log(encryptedMessage)
pm.environment.set("sign", encryptedMessage);

function generateRandomString(length = 32) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    return Array.from({ length }, () => chars[Math.floor(Math.random() * chars.length)]).join('');
}

// 定义 SHA-256 加密函数
function sha256Encrypt(message, sk) {
    // 使用 SHA-256 算法进行加密
    const hash = CryptoJS.HmacSHA256(message, sk);
    // 将加密结果转换为十六进制字符串
    return hash.toString(CryptoJS.enc.Hex);
}
```
