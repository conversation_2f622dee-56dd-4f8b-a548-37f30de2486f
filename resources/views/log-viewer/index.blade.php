<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志查看器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .log-content {
            font-family: monospace;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-2xl font-bold mb-6">日志查看器</h1>

        <div class="flex flex-col md:flex-row gap-6">
            <!-- 左侧日志文件列表 -->
            <div class="w-full md:w-1/4 bg-white rounded-lg shadow p-4">
                <h2 class="text-lg font-semibold mb-4">日志文件</h2>
                <div class="space-y-4">
                    @foreach($logDates as $date => $files)
                        <div class="border-b pb-2">
                            <h3 class="font-medium text-gray-700">{{ $date === 'current' ? '当前日志' : $date }}</h3>
                            <ul class="mt-2 space-y-1">
                                @foreach($files as $file)
                                    <li>
                                        <a href="#"
                                           class="log-file-link text-blue-500 hover:text-blue-700"
                                           data-file="{{ $file }}">
                                            {{ $file }}
                                        </a>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    @endforeach
                </div>
            </div>

            <!-- 右侧日志内容 -->
            <div class="w-full md:w-3/4 bg-white rounded-lg shadow p-4">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold" id="current-file">请选择日志文件</h2>
                    <div>
                        <input type="text" id="search-input"
                               class="px-3 py-2 border rounded-lg"
                               placeholder="搜索日志内容...">
                        <button id="search-btn"
                                class="ml-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                            搜索
                        </button>
                    </div>
                </div>
                <div id="log-content" class="log-content mt-4 p-4 bg-gray-50 rounded-lg h-[70vh] overflow-auto">
                    <div class="text-gray-500 text-center mt-20">
                        请从左侧选择日志文件查看内容
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const logLinks = document.querySelectorAll('.log-file-link');
            const logContent = document.getElementById('log-content');
            const currentFile = document.getElementById('current-file');
            const searchInput = document.getElementById('search-input');
            const searchBtn = document.getElementById('search-btn');

            let originalContent = '';

            // 点击日志文件链接
            logLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const file = this.getAttribute('data-file');

                    // 更新当前文件名
                    currentFile.textContent = file;

                    // 显示加载中
                    logContent.innerHTML = '<div class="text-center py-10">加载中...</div>';

                    // 获取日志内容
                    fetch(`/logs/view?file=${encodeURIComponent(file)}`)
                        .then(response => response.json())
                        .then(data => {
                            originalContent = data.content;
                            logContent.innerHTML = originalContent;
                            // 清空搜索框
                            searchInput.value = '';
                        })
                        .catch(error => {
                            logContent.innerHTML = `<div class="text-red-500">加载失败: ${error.message}</div>`;
                        });
                });
            });

            // 搜索功能
            searchBtn.addEventListener('click', function() {
                const searchTerm = searchInput.value.trim();
                if (!searchTerm || !originalContent) return;

                // 高亮搜索词
                const regex = new RegExp(searchTerm, 'gi');
                const highlightedContent = originalContent.replace(
                    regex,
                    match => `<mark class="bg-yellow-300">${match}</mark>`
                );

                logContent.innerHTML = highlightedContent;

                // 滚动到第一个匹配项
                const firstMatch = logContent.querySelector('mark');
                if (firstMatch) {
                    firstMatch.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            });

            // 按Enter键搜索
            searchInput.addEventListener('keyup', function(e) {
                if (e.key === 'Enter') {
                    searchBtn.click();
                }
            });
        });
    </script>
</body>
</html>
