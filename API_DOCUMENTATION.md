# API Documentation

This document provides information about the RESTful API endpoints available in this application.

## Authentication

All API requests require authentication using an API key. The API key should be included in the `X-API-Key` header.

Example:
```
X-API-Key: your-api-key
```

For demo purposes, you can use the API key: `demo-api-key`

## API Endpoints

### Base URL

All API endpoints are prefixed with `/api/v1`.

### Response Format

All API responses follow this standard format:

```json
{
  "code": 200,
  "message": "Success",
  "data": { ... }
}
```

For paginated responses:

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "items": [ ... ],
    "pagination": {
      "total": 100,
      "per_page": 10,
      "current_page": 1,
      "last_page": 10
    }
  }
}
```

For error responses:

```json
{
  "code": 400,
  "message": "Error message",
  "data": null
}
```

### Demo Resource

#### List Demos

```
GET /api/v1/demos
```

Query Parameters:
- `page` (optional): Page number (default: 1)
- `per_page` (optional): Items per page (default: 10)

Response:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "items": [
      {
        "id": 1,
        "name": "Demo 1",
        "description": "This is demo 1"
      },
      {
        "id": 2,
        "name": "Demo 2",
        "description": "This is demo 2"
      }
    ],
    "pagination": {
      "total": 3,
      "per_page": 10,
      "current_page": 1,
      "last_page": 1
    }
  }
}
```

#### Get Demo

```
GET /api/v1/demos/{id}
```

Response:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "id": 1,
    "name": "Demo 1",
    "description": "This is demo 1"
  }
}
```

#### Create Demo

```
POST /api/v1/demos
```

Request Body:
```json
{
  "name": "New Demo",
  "description": "This is a new demo"
}
```

Response:
```json
{
  "code": 201,
  "message": "Demo created successfully",
  "data": {
    "id": 11,
    "name": "New Demo",
    "description": "This is a new demo",
    "created_at": "2023-05-01 12:00:00"
  }
}
```

#### Update Demo

```
PUT /api/v1/demos/{id}
```

Request Body:
```json
{
  "name": "Updated Demo",
  "description": "This is an updated demo"
}
```

Response:
```json
{
  "code": 200,
  "message": "Demo updated successfully",
  "data": {
    "id": 1,
    "name": "Updated Demo",
    "description": "This is an updated demo",
    "updated_at": "2023-05-01 12:00:00"
  }
}
```

#### Delete Demo

```
DELETE /api/v1/demos/{id}
```

Response:
```json
{
  "code": 200,
  "message": "Demo deleted successfully",
  "data": null
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 400  | Bad Request |
| 401  | Unauthorized |
| 403  | Forbidden |
| 404  | Not Found |
| 405  | Method Not Allowed |
| 422  | Validation Failed |
| 429  | Too Many Requests |
| 500  | Server Error |
| 1001 | Invalid API Key |
| 1002 | API Key Expired |
| 1003 | API Rate Limit Exceeded |

## Versioning

The API is versioned using URL path versioning. The current version is `v1`.

## Rate Limiting

API requests are subject to rate limiting. The current limit is 60 requests per minute per API key.
