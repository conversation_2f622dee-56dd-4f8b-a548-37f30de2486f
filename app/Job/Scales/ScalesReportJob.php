<?php

namespace App\Job\Scales;

use App\Service\Scales\ScalesRunningService;
use App\Service\Scales\ScalesService;
use Hyperf\AsyncQueue\Job;
use function Hyperf\Support\make;

class ScalesReportJob extends Job
{
    public array $params;

    /**
     * 任务执行失败后的重试次数，即最大执行次数为 $maxAttempts+1 次
     */
    protected int $maxAttempts = 2;

    public function __construct($params)
    {
        // 这里最好是普通数据，不要使用携带 IO 的对象，比如 PDO 对象
        $this->params = $params;
    }

    /**
     * 消费逻辑
     * @inheritDoc
     * @throws \Exception
     */
    public function handle()
    {
        $runningService = make(ScalesRunningService::class);
        return $runningService->getScalesReport($this->params['reportUuid']);
    }
}
