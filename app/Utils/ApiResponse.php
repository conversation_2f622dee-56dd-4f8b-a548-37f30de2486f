<?php

declare(strict_types=1);
/**
 * API Response utility class
 * 
 * This class provides standardized response formats for the API
 */

namespace App\Utils;

use Hyperf\HttpServer\Contract\ResponseInterface;

class ApiResponse
{
    /**
     * Success response
     * 
     * @param mixed $data The data to return
     * @param string $message Success message
     * @param int $code HTTP status code
     * @return array
     */
    public static function success($data = null, string $message = 'Success', int $code = 200): array
    {
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * Error response
     * 
     * @param string $message Error message
     * @param int $code Error code
     * @param mixed $data Additional error data
     * @return array
     */
    public static function error(string $message = 'Error', int $code = 400, $data = null): array
    {
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * Paginated response
     * 
     * @param array $items Items for the current page
     * @param int $total Total number of items
     * @param int $page Current page number
     * @param int $perPage Number of items per page
     * @param string $message Success message
     * @param int $code HTTP status code
     * @return array
     */
    public static function paginated(array $items, int $total, int $page, int $perPage, string $message = 'Success', int $code = 200): array
    {
        return [
            'code' => $code,
            'message' => $message,
            'data' => [
                'items' => $items,
                'pagination' => [
                    'total' => $total,
                    'per_page' => $perPage,
                    'current_page' => $page,
                    'last_page' => (int) ceil($total / $perPage),
                ],
            ],
        ];
    }
}
