<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

namespace App\Exception\Handler;


use App\Constants\Entity\ResponseEntity;
use App\Constants\ResponseExceptionCode;
use Hyperf\Contract\StdoutLoggerInterface;
use Hyperf\ExceptionHandler\ExceptionHandler;
use Hyperf\HttpMessage\Stream\SwooleStream;
use Hyperf\Validation\ValidationException;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Throwable;
use function Hyperf\Stringable\str;

class ValidationExceptionHandler extends ExceptionHandler
{

    private StdoutLoggerInterface $logger;
    private ServerRequestInterface $request;

    public function __construct(StdoutLoggerInterface $logger, ServerRequestInterface $request)
    {
        $this->logger = $logger;
        $this->request = $request;
    }

    public function handle(Throwable $throwable, ResponseInterface $response): ResponseInterface
    {
        $this->stopPropagation();
        /** @var ValidationException $throwable */

        $responseEntity = new ResponseEntity(ResponseExceptionCode::PARAM_ERROR_HTTP_CODE,
            ResponseExceptionCode::PARAM_ERROR_MESSAGE, $throwable->validator->errors()->all());
        if (!$response->hasHeader('content-type')) {
            $response = $response->withAddedHeader('content-type', 'application/json; charset=utf-8');
        }

        $this->logger->alert($responseEntity->toJson());

        return $response->withStatus(ResponseExceptionCode::PARAM_ERROR_HTTP_CODE)->withBody(new SwooleStream($responseEntity->toJson()));
    }

    public function isValid(Throwable $throwable): bool
    {
        return $throwable instanceof ValidationException;
    }
}
