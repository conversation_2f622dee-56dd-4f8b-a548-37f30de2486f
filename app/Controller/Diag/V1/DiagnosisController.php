<?php

namespace App\Controller\Diag\V1;

use App\Controller\Diag\AbstractDiagApiController;
use App\Request\DiagnosisReportRequest;
use App\Request\FaceDiagnosisRequest;
use App\Service\Diag\DiagnosisService;
use function Hyperf\Support\make;

class DiagnosisController extends AbstractDiagApiController
{

    /**
     * 提交舌诊图片
     * @param FaceDiagnosisRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function faceDiagnosis(FaceDiagnosisRequest $request): \Psr\Http\Message\ResponseInterface
    {
        $params = $request->validated();
        if (empty($params['ffImage']) && empty($params['tfImage']) && empty($params['tbImage'])) {
            return $this->error('At least one of ffImage, tfImage, or tbImage must be provided.');
        }
        $service = make(DiagnosisService::class);
        try {
            $result = $service->faceDiagnosis($params);
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error('舌诊数据提交失败 ' . $e->getMessage());
        }
    }

    /**
     * 根据问诊问题获取报告
     * @param DiagnosisReportRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function faceDiagnosisReport(DiagnosisReportRequest $request): \Psr\Http\Message\ResponseInterface
    {
        $params = $request->validated();
        $service = make(DiagnosisService::class);
        try {
            $result = $service->diagnosisReport($params);
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error('舌诊报告获取失败 ' . $e->getMessage());
        }
    }
}
