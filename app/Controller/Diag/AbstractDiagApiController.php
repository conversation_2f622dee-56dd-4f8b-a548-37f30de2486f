<?php

declare(strict_types=1);
/**
 * Base API Controller
 *
 * This abstract class provides common functionality for all API controllers
 */

namespace App\Controller\Diag;

use App\Controller\AbstractController;
use App\Utils\ApiResponse;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\HttpServer\Contract\ResponseInterface;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\ContainerInterface;
use Psr\Container\NotFoundExceptionInterface;

abstract class AbstractDiagApiController extends AbstractController
{
    /**
     * @var ContainerInterface
     */
    protected ContainerInterface $container;

    /**
     * @var RequestInterface
     */
    protected RequestInterface $request;

    /**
     * @var ResponseInterface
     */
    protected ResponseInterface $response;

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
        $this->request = $container->get(RequestInterface::class);
        $this->response = $container->get(ResponseInterface::class);
    }

    /**
     * Return a success response
     *
     * @param mixed $data
     * @param string $message
     * @param int $code
     * @return \Psr\Http\Message\ResponseInterface
     */
    protected function success($data = null, string $message = 'Success', int $code = 200): \Psr\Http\Message\ResponseInterface
    {
        return $this->response->json(
            $data
        );
    }

    /**
     * Return an error response
     *
     * @param string $message
     * @param int $code
     * @param mixed|null $data
     * @return \Psr\Http\Message\ResponseInterface
     */
    protected function error(string $message = 'Error', int $code = 400, mixed $data = null): \Psr\Http\Message\ResponseInterface
    {
        return $this->response->json(
            $data
        )->withStatus($code < 600 ? $code : 500);
    }

}
