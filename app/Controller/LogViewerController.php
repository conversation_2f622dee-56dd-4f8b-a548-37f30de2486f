<?php

declare(strict_types=1);

namespace App\Controller;

use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\HttpServer\Contract\ResponseInterface;
use Hyperf\View\RenderInterface;

class LogViewerController
{
    protected RenderInterface $render;

    public function __construct(RenderInterface $render)
    {
        $this->render = $render;
    }

    public function index(RequestInterface $request, ResponseInterface $response)
    {
        $logsDir = BASE_PATH . '/runtime/logs/';
        $logDates = [];
        
        // 获取日志文件列表并按日期分组
        if (is_dir($logsDir)) {
            $files = scandir($logsDir);
            foreach ($files as $file) {
                if ($file != '.' && $file != '..' && is_file($logsDir . $file)) {
                    // 从文件名中提取日期 (hyperf-YYYY-MM-DD.log 或 hyperf.log.YYYY-MM-DD)
                    if (preg_match('/hyperf-(\d{4}-\d{2}-\d{2})\.log/', $file, $matches)) {
                        $date = $matches[1];
                        $logDates[$date][] = $file;
                    } elseif (preg_match('/hyperf\.log\.(\d{4}-\d{2}-\d{2})/', $file, $matches)) {
                        $date = $matches[1];
                        $logDates[$date][] = $file;
                    } elseif ($file === 'hyperf.log') {
                        $date = 'current';
                        $logDates[$date][] = $file;
                    }
                }
            }
        }
        
        // 按日期倒序排列
        krsort($logDates);
        
        return $this->render->render('log-viewer/index', [
            'logDates' => $logDates,
        ]);
    }
    
    public function view(RequestInterface $request, ResponseInterface $response)
    {
        $file = $request->input('file');
        $logsDir = BASE_PATH . '/runtime/logs/';
        $content = '';
        
        // 安全检查，防止目录遍历
        $filePath = realpath($logsDir . $file);
        if ($filePath && strpos($filePath, $logsDir) === 0 && is_file($filePath)) {
            $content = file_get_contents($filePath);
            
            // 格式化和高亮日志内容
            $content = $this->formatLogContent($content);
        }
        
        return $response->json([
            'content' => $content
        ]);
    }
    
    protected function formatLogContent(string $content): string
    {
        // 将日志级别用不同颜色高亮
        $content = preg_replace('/\[(DEBUG|INFO|NOTICE)\]/', '<span class="text-blue-500">[$1]</span>', $content);
        $content = preg_replace('/\[(WARNING|ALERT)\]/', '<span class="text-yellow-500">[$1]</span>', $content);
        $content = preg_replace('/\[(ERROR|CRITICAL|EMERGENCY)\]/', '<span class="text-red-500">[$1]</span>', $content);
        
        // 高亮日期时间
        $content = preg_replace('/(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})/', '<span class="text-green-500">$1</span>', $content);
        
        // 高亮JSON内容
        $content = preg_replace_callback('/(\{.*?\})/', function($matches) {
            $json = $matches[1];
            try {
                $decoded = json_decode($json, true);
                if ($decoded) {
                    return '<pre class="bg-gray-100 p-2 rounded">' . json_encode($decoded, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
                }
            } catch (\Exception $e) {
                // 忽略无效的JSON
            }
            return $matches[0];
        }, $content);
        
        // 将换行符转换为HTML换行
        $content = nl2br($content);
        
        return $content;
    }
}