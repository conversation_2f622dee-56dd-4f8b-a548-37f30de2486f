<?php

namespace App\Controller\Ocr\V1;

use App\Controller\Ocr\AbstractOcrApiController;
use App\Request\OcrIdentifyRequest;
use App\Service\Ocr\OcrFactory;
use function Hyperf\Support\make;

class IdentifyController extends AbstractOcrApiController
{
    /**
     * OCR识别
     * @param OcrIdentifyRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function ocr(OcrIdentifyRequest $request)
    {
        $params = $request->validated();
        $ocrType = $params['type'] ?? 'tencent';
        $ocrFactory = make(OcrFactory::class)->ocrIdentifyObj($ocrType);
        try {
            $result = $ocrFactory->ocrIdentify($params);
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error('OCR识别失败 ' . $e->getMessage());
        }

    }
}
