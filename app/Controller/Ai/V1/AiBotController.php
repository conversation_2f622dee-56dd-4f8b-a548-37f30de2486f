<?php

namespace App\Controller\Ai\V1;

use App\Constants\ContextConst;
use App\Controller\Ai\AbstractAiApiController;
use App\Request\AiChatRequest;
use App\Service\Ai\AiFactory;
use Hyperf\Context\Context;
use function Hyperf\Support\make;

class AiBotController extends AbstractAiApiController
{

    /**
     * AI Chat
     * @param AiChatRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function chat(AiChatRequest $request): \Psr\Http\Message\ResponseInterface
    {
        $params = $request->validated();
        $aiType = Context::get(ContextConst::AI_TYPE);
        $aiFactory = make(AiFactory::class)->service($aiType);
        try {
            $result = $aiFactory->chatCompletions($params['message'], $params['systemRule'] ?? '');
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error('AI Chat Error ' . $e->getMessage());
        }
    }
}
