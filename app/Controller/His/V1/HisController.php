<?php

namespace App\Controller\His\V1;

use App\Constants\Entity\AppointmentRegistrationEntity;
use App\Constants\Entity\PatientSaveEntity;
use App\Controller\His\AbstractHisApiController;
use App\Request\AppointmentRegistrationRequest;
use App\Request\DoctorByDepartmentRequest;
use App\Request\DoctorSchedulingRequest;
use App\Request\InvalidSettlementListRequest;
use App\Request\MedicalRecordRequest;
use App\Request\PatientCardNumRequest;
use App\Request\PatientListRequest;
use App\Request\PatientPayRequest;
use App\Request\PatientQueryRequest;
use App\Request\PatientSaveRequest;
use App\Request\PatientSetDefaultRequest;
use App\Request\PaymentListRequest;
use App\Request\RegistrationCancelRequest;
use App\Request\RegistrationPayRequest;
use App\Request\RegistrationQueryRequest;
use App\Request\RegistrationRefundRequest;
use App\Service\His\HisService;
use App\Service\HostService;
use Hyperf\HttpServer\Contract\RequestInterface;
use function Hyperf\Support\make;

class HisController extends AbstractHisApiController
{
    /**
     * 保存、编辑就诊人
     * @param PatientSaveRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function patientSave(PatientSaveRequest $request): \Psr\Http\Message\ResponseInterface
    {
        $params = $request->validated();
        try {
            $patientSaveEntity = new PatientSaveEntity($params);
        } catch (\Exception $e) {
            return $this->error('参数错误 ' . $e->getMessage());
        }

        $service = make(HisService::class);
        try {
            $res = $service->savePatient($patientSaveEntity);
            return $this->success($res);
        } catch (\Exception $e) {
            return $this->error('保存就诊人失败 ' . $e->getMessage());
        }
    }

    /**
     * 查询就诊人信息
     * @param PatientQueryRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function patientQuery(PatientQueryRequest $request): \Psr\Http\Message\ResponseInterface
    {
        $params = $request->validated();
        $cardNo = $params['cardNo'];

        $service = make(HisService::class);
        try {
            $departmentList = $service->getPatientInfo($cardNo);
            return $this->success($departmentList);
        } catch (\Exception $e) {
            return $this->error('获取就诊人信息失败 ' . $e->getMessage());
        }
    }

    /**
     * 获取医院科室列表
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function getHospitalDepartmentList(): \Psr\Http\Message\ResponseInterface
    {
        $service = make(HisService::class);
        try {
            $departmentList = $service->getDepartmentList();
            return $this->success($departmentList);
        } catch (\Exception $e) {
            return $this->error('获取科室列表失败 ' . $e->getMessage() . $e->getFile() . $e->getLine());
        }
    }

    /**
     * 获取科室医生列表
     * @param DoctorByDepartmentRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function getDoctorListByDepartment(DoctorByDepartmentRequest $request): \Psr\Http\Message\ResponseInterface
    {
        $params = $request->validated();
        $deptId = $params['deptId'];
        $startTime = $params['startTime'] ?? date('Y-m-d');

        $service = make(HisService::class);
        try {
            $doctorList = $service->getDoctorList($deptId, $startTime);
            return $this->success($doctorList);
        } catch (\Exception $e) {
            return $this->error('获取医生列表失败 ' . $e->getFile() . '-' . $e->getLine() . '-' . $e->getMessage());
        }
    }

    /**
     * 查询医生排班列表
     * @param DoctorSchedulingRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function getDoctorSchedulingList(DoctorSchedulingRequest $request): \Psr\Http\Message\ResponseInterface
    {
        $params = $request->validated();
        $deptId = $params['deptId'];
        $doctorCd = $params['doctorCd'];
        $startTime = $params['startTime'] ?? date('Y-m-d');

        $service = make(HisService::class);
        try {
            $schedulingList = $service->getDoctorSchedulingList($deptId, $doctorCd, $startTime);
            return $this->success($schedulingList);
        } catch (\Exception $e) {
            return $this->error('获取医生排班列表失败 ' . $e->getMessage());
        }
    }

    /**
     * 预约挂号提交
     * @param AppointmentRegistrationRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function appointmentRegistration(AppointmentRegistrationRequest $request): \Psr\Http\Message\ResponseInterface
    {
        $params = $request->validated();
        try {
            $appointmentEntity = new AppointmentRegistrationEntity($params);
        } catch (\Exception $e) {
            return $this->error('参数错误 ' . $e->getMessage());
        }

        $service = make(HisService::class);
        try {
            $res = $service->appointmentRegistration($appointmentEntity);
            return $this->success($res);
        } catch (\Exception $e) {
            return $this->error('预约挂号提交失败 ' . $e->getMessage());
        }
    }

    /**
     * 取消预约挂号
     * 未支付才能取消
     * @param RegistrationCancelRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function registrationCancel(RegistrationCancelRequest $request): \Psr\Http\Message\ResponseInterface
    {
        $params = $request->validated();
        $service = make(HisService::class);
        try {
            $res = $service->registrationCancel($params['cardNo'], $params['sarid']);
            return $this->success($res);
        } catch (\Exception $exception) {
            return $this->error('取消预约失败 ' . $exception->getMessage());
        }
    }

    /**
     * 预约退号
     * @param RegistrationRefundRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function registrationRefund(RegistrationRefundRequest $request): \Psr\Http\Message\ResponseInterface
    {
        $params = $request->validated();
        $service = make(HisService::class);
        try {
            $res = $service->registrationRefund($params['cardNo'], $params['sarid'], $params['receiMoney']);
            return $this->success($res);
        } catch (\Exception $exception) {
            return $this->error('预约退号失败 ' . $exception->getMessage());
        }
    }

    /**
     * 挂号结算
     * @param RegistrationPayRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function registrationPay(RegistrationPayRequest $request): \Psr\Http\Message\ResponseInterface
    {
        $params = $request->validated();
        $service = make(HisService::class);
        try {
            $res = $service->registrationPay($params['cardNo'], $params['sarid'], $params['receiMoney'], $params['payMethod']);
            return $this->success($res);
        } catch (\Exception $exception) {
            return $this->error('预约退号失败 ' . $exception->getMessage());
        }
    }


    /**
     * 查询挂号列表
     * @param RegistrationQueryRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function registrationList(RegistrationQueryRequest $request): \Psr\Http\Message\ResponseInterface
    {
        $params = $request->validated();
        $service = make(HisService::class);
        try {
            $res = $service->getRegistrationList($params['phone'], $params['cardNo']);
            return $this->success($res);
        } catch (\Exception $exception) {
            return $this->error('取消预约失败 ' . $exception->getMessage());
        }
    }


    /**
     * Add a debug endpoint to check if files exist
     * @param string $filename
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function checkImageExists(string $filename, RequestInterface $request): \Psr\Http\Message\ResponseInterface
    {
        $path = BASE_PATH . '/public/doctor/avatar/' . $filename;
        $host = make(HostService::class)->getHost($request);
        return $this->success([
            'exists' => file_exists($path),
            'path' => $path,
            'readable' => is_readable($path),
            'size' => file_exists($path) ? filesize($path) : 0,
            'url' => $host . '/doctor/avatar/' . $filename,
        ]);
    }

    /**
     * 设置默认就诊人
     * @param PatientSetDefaultRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function patientSetDefault(PatientSetDefaultRequest $request): \Psr\Http\Message\ResponseInterface
    {
        $params = $request->validated();
        $service = make(HisService::class);
        try {
            $res = $service->userSetDefaultPatient($params['openid'], $params['cardNo']);
            return $this->success($res);
        } catch (\Exception $e) {
            return $this->error('设置默认就诊人失败 ' . $e->getMessage());
        }
    }

    /**
     * 解绑就诊人
     * @param PatientSetDefaultRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function patientUnbind(PatientSetDefaultRequest $request): \Psr\Http\Message\ResponseInterface
    {
        $params = $request->validated();
        $service = make(HisService::class);
        try {
            $res = $service->userUnbindPatient($params['openid'], $params['cardNo']);
            return $this->success($res);
        } catch (\Exception $e) {
            return $this->error('就诊人解绑提交失败 ' . $e->getMessage());
        }
    }

    /**
     * 查询就诊人列表
     * @param PatientListRequest $request
     * @return \Psr\Http\Message\ResponseInterfaceq
     */
    public function patientList(PatientListRequest $request): \Psr\Http\Message\ResponseInterface
    {
        $params = $request->validated();
        $service = make(HisService::class);
        try {
            $res = $service->getUserPatientList($params['openid']);
            return $this->success($res);
        } catch (\Exception $e) {
            return $this->error('查询就诊人列表 ' . $e->getMessage());
        }
    }

    /**
     * 查询就诊人门诊待缴费列表
     * @param PatientQueryRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function outpatientPayment(PatientQueryRequest $request): \Psr\Http\Message\ResponseInterface
    {
        $params = $request->validated();
        $service = make(HisService::class);
        try {
            $res = $service->getPatientPres($params['cardNo']);
            return $this->success($res);
        } catch (\Exception $e) {
            return $this->error('查询就诊人门诊待缴费列表 ' . $e->getMessage());
        }
    }

    /**
     * 就诊人门诊缴费结算
     * @param PatientPayRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function patientPay(PatientPayRequest $request): \Psr\Http\Message\ResponseInterface
    {
        $params = $request->validated();
        $service = make(HisService::class);
        try {
            $res = $service->patientPresPay($params['cardNo'], $params['hisOrderNo'], $params['hisOrderType'], $params['payMethod'], $params['payAmount']);
            return $this->success($res);
        } catch (\Exception $e) {
            return $this->error('门诊缴费结算失败 ' . $e->getMessage());
        }
    }

    /**
     * 查询缴费记录
     * @param PaymentListRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function paymentList(PaymentListRequest $request): \Psr\Http\Message\ResponseInterface
    {
        $params = $request->validated();
        $service = make(HisService::class);
        try {
            $res = $service->getPaymentList($params['idCardNo'], $params['beginDate'], $params['endDate']);
            return $this->success($res);
        } catch (\Exception $e) {
            return $this->error('查询就诊人门诊待缴费列表 ' . $e->getMessage());
        }
    }

    /**
     * 获取处方列表
     * @param PaymentListRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function prescriptionList(PaymentListRequest $request): \Psr\Http\Message\ResponseInterface
    {
        $params = $request->validated();
        $service = make(HisService::class);
        try {
            $res = $service->getPrescriptionList($params['idCardNo'], $params['beginDate'], $params['endDate']);
            return $this->success($res);
        } catch (\Exception $e) {
            return $this->error('查询就诊人门诊处方列表 ' . $e->getMessage());
        }
    }


    /**
     * 就诊人身份证和就诊卡号查询
     * @param PatientCardNumRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function patientCardNumber(PatientCardNumRequest $request): \Psr\Http\Message\ResponseInterface
    {
        $params = $request->validated();
        $service = make(HisService::class);
        if ($params['idCardNo'] == '' && $params['cardNo'] == '') {
            return $this->error('身份证号和就诊卡号不能同时为空');
        }
        try {
            $res = $service->getPatientCardNumberInfo($params['idCardNo'], $params['cardNo']);
            return $this->success($res);
        } catch (\Exception $e) {
            return $this->error('查询就诊人信息错误 ' . $e->getMessage());
        }
    }

    /**
     * 查询档案列表
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function getHisAllPatientList(): \Psr\Http\Message\ResponseInterface
    {
        $service = make(HisService::class);
        try {
            $res = $service->getHisAllPatientList();
            return $this->success($res);
        } catch (\Exception $e) {
            return $this->error('查询档案列表信息错误 ' . $e->getMessage());
        }
    }

    /**
     * 查询就诊记录
     * @param PatientQueryRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function getAttendanceRecords(PatientQueryRequest $request): \Psr\Http\Message\ResponseInterface
    {
        $params = $request->validated();
        $service = make(HisService::class);
        try {
            $res = $service->getAttendanceRecordsList($params['cardNo']);
            return $this->success($res);
        } catch (\Exception $e) {
            return $this->error('查询就诊记录信息错误 ' . $e->getMessage());
        }
    }

    /**
     * 查询就诊记录的病历信息
     * @param MedicalRecordRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function getMedicalRecord(MedicalRecordRequest $request): \Psr\Http\Message\ResponseInterface
    {
        $params = $request->validated();
        $service = make(HisService::class);
        try {
            $res = $service->getMedicalRecordList($params['recordId']);
            return $this->success($res);
        } catch (\Exception $e) {
            return $this->error('查询病历信息错误 ' . $e->getMessage());
        }
    }

    /**
     * 查询作废结算记录
     * @param InvalidSettlementListRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function getInvalidSettlementList(InvalidSettlementListRequest $request): \Psr\Http\Message\ResponseInterface
    {
        $params = $request->validated();
        $service = make(HisService::class);
        try {
            $res = $service->getInvalidSettlementList($params['cardNo'] ?? '', $params['hisOrderNo'] ?? '');
            return $this->success($res);
        } catch (\Exception $e) {
            return $this->error('查询作废结算记录错误 ' . $e->getMessage());
        }
    }
}
