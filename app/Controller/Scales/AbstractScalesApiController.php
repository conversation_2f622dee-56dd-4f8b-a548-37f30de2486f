<?php

declare(strict_types=1);
/**
 * Base API Controller
 *
 * This abstract class provides common functionality for all API controllers
 */

namespace App\Controller\Scales;

use App\Controller\AbstractController;
use App\Utils\ApiResponse;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\HttpServer\Contract\ResponseInterface;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\ContainerInterface;
use Psr\Container\NotFoundExceptionInterface;

abstract class AbstractScalesApiController extends AbstractController
{
    /**
     * @var ContainerInterface
     */
    protected ContainerInterface $container;

    /**
     * @var RequestInterface
     */
    protected RequestInterface $request;

    /**
     * @var ResponseInterface
     */
    protected ResponseInterface $response;

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
        $this->request = $container->get(RequestInterface::class);
        $this->response = $container->get(ResponseInterface::class);
    }

    /**
     * Return a success response
     *
     * @param mixed $data
     * @param string $message
     * @param int $code
     * @return \Psr\Http\Message\ResponseInterface
     */
    protected function success($data = null, string $message = 'Success', int $code = 200): \Psr\Http\Message\ResponseInterface
    {
        return $this->response->json(
            ApiResponse::success($data, $message, $code)
        );
    }

    /**
     * Return an error response
     *
     * @param string $message
     * @param int $code
     * @param mixed|null $data
     * @return \Psr\Http\Message\ResponseInterface
     */
    protected function error(string $message = 'Error', int $code = 400, mixed $data = null): \Psr\Http\Message\ResponseInterface
    {
        return $this->response->json(
            ApiResponse::error($message, $code, $data)
        )->withStatus($code < 600 ? $code : 500);
    }

    /**
     * Return a paginated response
     *
     * @param array $items
     * @param int $total
     * @param int $page
     * @param int $perPage
     * @param string $message
     * @param int $code
     * @return \Psr\Http\Message\ResponseInterface
     */
    protected function paginated(array $items, int $total, int $page, int $perPage, string $message = 'Success', int $code = 200): \Psr\Http\Message\ResponseInterface
    {
        return $this->response->json(
            ApiResponse::paginated($items, $total, $page, $perPage, $message, $code)
        );
    }
}
