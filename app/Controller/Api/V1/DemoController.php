<?php

declare(strict_types=1);
/**
 * Demo API Controller
 * 
 * This controller demonstrates RESTful API endpoints
 */

namespace App\Controller\Api\V1;

use App\Controller\Api\AbstractApiController;
use App\Constants\ErrorCode;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\ApiAuthMiddleware;

/**
 * @Controller(prefix="api/v1/demos")
 * @Middleware(ApiAuthMiddleware::class)
 */
class DemoController extends AbstractApiController
{
    /**
     * Get a list of demo resources
     * 
     * @RequestMapping(path="", methods="GET")
     * 
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function index()
    {
        // Demo data - in a real application, this would come from a database
        $items = [
            ['id' => 1, 'name' => 'Demo 1', 'description' => 'This is demo 1'],
            ['id' => 2, 'name' => 'Demo 2', 'description' => 'This is demo 2'],
            ['id' => 3, 'name' => 'Demo 3', 'description' => 'This is demo 3'],
        ];
        
        // Get pagination parameters
        $page = (int) $this->request->input('page', 1);
        $perPage = (int) $this->request->input('per_page', 10);
        
        return $this->paginated($items, count($items), $page, $perPage);
    }
    
    /**
     * Get a specific demo resource
     * 
     * @RequestMapping(path="{id}", methods="GET")
     * 
     * @param int $id
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function show(int $id)
    {
        // Demo data - in a real application, this would come from a database
        $demo = ['id' => $id, 'name' => "Demo {$id}", 'description' => "This is demo {$id}"];
        
        // Check if resource exists
        if ($id > 10) {
            return $this->error('Demo not found', ErrorCode::NOT_FOUND);
        }
        
        return $this->success($demo);
    }
    
    /**
     * Create a new demo resource
     * 
     * @RequestMapping(path="", methods="POST")
     * 
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function store()
    {
        // Get request data
        $name = $this->request->input('name');
        $description = $this->request->input('description');
        
        // Validate input
        if (empty($name)) {
            return $this->error('Name is required', ErrorCode::VALIDATION_ERROR);
        }
        
        // Demo data - in a real application, this would be saved to a database
        $demo = [
            'id' => 11, // Demo ID
            'name' => $name,
            'description' => $description ?? '',
            'created_at' => date('Y-m-d H:i:s'),
        ];
        
        return $this->success($demo, 'Demo created successfully', 201);
    }
    
    /**
     * Update an existing demo resource
     * 
     * @RequestMapping(path="{id}", methods="PUT")
     * 
     * @param int $id
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function update(int $id)
    {
        // Check if resource exists
        if ($id > 10) {
            return $this->error('Demo not found', ErrorCode::NOT_FOUND);
        }
        
        // Get request data
        $name = $this->request->input('name');
        $description = $this->request->input('description');
        
        // Validate input
        if (empty($name)) {
            return $this->error('Name is required', ErrorCode::VALIDATION_ERROR);
        }
        
        // Demo data - in a real application, this would update a database record
        $demo = [
            'id' => $id,
            'name' => $name,
            'description' => $description ?? '',
            'updated_at' => date('Y-m-d H:i:s'),
        ];
        
        return $this->success($demo, 'Demo updated successfully');
    }
    
    /**
     * Delete a demo resource
     * 
     * @RequestMapping(path="{id}", methods="DELETE")
     * 
     * @param int $id
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function destroy(int $id)
    {
        // Check if resource exists
        if ($id > 10) {
            return $this->error('Demo not found', ErrorCode::NOT_FOUND);
        }
        
        // Demo data - in a real application, this would delete from a database
        
        return $this->success(null, 'Demo deleted successfully');
    }
}
