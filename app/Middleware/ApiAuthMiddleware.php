<?php

declare(strict_types=1);
/**
 * API Authentication Middleware
 *
 * This middleware handles API authentication using API keys
 */

namespace App\Middleware;

use App\Constants\ContextConst;
use App\Constants\ErrorCode;
use App\Model\LyHospital;
use App\Utils\ApiResponse;
use Hyperf\Context\Context;
use Hyperf\HttpServer\Contract\ResponseInterface as HttpResponse;
use Hyperf\Redis\Redis;
use Psr\Container\ContainerInterface;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\RequestHandlerInterface;

class ApiAuthMiddleware implements MiddlewareInterface
{
    /**
     * @var ContainerInterface
     */
    protected ContainerInterface $container;

    /**
     * @var HttpResponse
     */
    protected HttpResponse $response;

    protected Redis $redis;

    public function __construct(ContainerInterface $container, HttpResponse $response, Redis $redis)
    {
        $this->container = $container;
        $this->response = $response;
        $this->redis = $redis;
    }

    /**
     * Process an incoming server request
     *
     * @param ServerRequestInterface $request
     * @param RequestHandlerInterface $handler
     * @return ResponseInterface
     */
    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        // 1. 验证基础请求头
        $authorizationSign = $request->getHeaderLine('Authorization');
        if (empty($authorizationSign)) {
            return $this->unauthorizedResponse('Sign Authorization is required');
        }

        $timestamp = $request->getHeaderLine('X-Timestamp');
        $accessKey = $request->getHeaderLine('X-Access-Key');
        $nonce = $request->getHeaderLine('X-Nonce');

        // 2. 验证必要请求头
        if (empty($timestamp) || empty($accessKey)) {
            return $this->unauthorizedResponse('Timestamp or Access-key is empty');
        }

        // 3. 验证时间戳有效性
        if (abs((int)$timestamp - time()) > 60) {
            return $this->unauthorizedResponse('Timestamp is invalid');
        }

        // 4. 获取机构信息（缓存优先）
        $registerCompany = $this->getRegisterCompany($accessKey);
        if (!$registerCompany) {
            return $this->unauthorizedResponse('The organization is not registered', ErrorCode::INVALID_API_KEY);
        }

        // 5. 验证签名
        if (!$this->validateSignature($authorizationSign, $timestamp, $nonce, $accessKey, $registerCompany['sk'])) {
            return $this->unauthorizedResponse('Sign Authorization is invalid', ErrorCode::INVALID_API_KEY);
        }

        // 6. 设置上下文信息
        $this->setContext($accessKey, $registerCompany);

        return $handler->handle($request);
    }

    private function unauthorizedResponse(string $message, int $errorCode = ErrorCode::UNAUTHORIZED): ResponseInterface
    {
        return $this->response->json(
            ApiResponse::error($message, $errorCode)
        )->withStatus(401);
    }

    private function getRegisterCompany(string $accessKey): ?array
    {
        $cacheKey = 'register_company_hospital_' . $accessKey;
        $cachedData = $this->redis->get($cacheKey);

        if ($cachedData) {
            return json_decode($cachedData, true);
        }

        $fields = [
            'id', 'company_party_id', 'name', 'appid', 'sk', 'config_id',
            'version', 'domain', 'app_code', 'ai_type', 'model', 'ai_token'
        ];

        $registerCompanyInfo = (new LyHospital())
            ->where('ak', $accessKey)
            ->where('status', 1)
            ->select($fields)
            ->first();

        if (!$registerCompanyInfo) {
            return null;
        }

        $companyData = $registerCompanyInfo->toArray();
        $this->redis->set($cacheKey, json_encode($companyData), 86400);

        return $companyData;
    }

    private function validateSignature(
        string $authSign,
        string $timestamp,
        string $nonce,
        string $accessKey,
        string $secretKey
    ): bool {
        $signData = [$timestamp, $nonce, $accessKey];
        $signDataString = implode('', $signData);
        $checkSign = hash_hmac('sha256', $signDataString, $secretKey);

        return hash_equals($authSign, $checkSign);
    }

    private function setContext(string $accessKey, array $company): void
    {
        $contextMap = [
            ContextConst::AK => $accessKey,
            ContextConst::HOSPITAL_ID => $company['id'],
            ContextConst::COMPANY_PARTY_ID => $company['company_party_id'],
            ContextConst::APPID => $company['appid'],
            ContextConst::HOSPITAL_NAME => $company['name'],
            ContextConst::CONFIG_ID => $company['config_id'],
            ContextConst::DOMAIN => $company['domain'],
            ContextConst::VERSION => $company['version'],
            ContextConst::APP_CODE => $company['app_code'],
            ContextConst::AI_TYPE => $company['ai_type'],
            ContextConst::AI_MODEL => $company['model'],
            ContextConst::AI_TOKEN => $company['ai_token'],
        ];

        foreach ($contextMap as $key => $value) {
            Context::set($key, $value);
        }
    }
}
