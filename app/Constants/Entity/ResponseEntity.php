<?php
declare(strict_types=1);
namespace App\Constants\Entity;

class ResponseEntity
{
    public int $code;
    public string $message;
    public array $data;

    public function __construct(int $code, string $message, array $data)
    {
        $this->code = $code;
        $this->message = $message;
        $this->data = $data;
    }

    public function toJson(): string
    {
        return json_encode([
            'code' => $this->code,
            'message' => $this->message,
            'data' => $this->data
        ], JSON_UNESCAPED_UNICODE);
    }

    public function toArray(): array
    {
        return [
            'code' => $this->code,
            'message' => $this->message,
            'data' => $this->data
        ];
    }
}
