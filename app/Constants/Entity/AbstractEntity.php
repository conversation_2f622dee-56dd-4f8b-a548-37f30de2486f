<?php

namespace App\Constants\Entity;

use App\Exception\RuntimeException;

abstract class AbstractEntity
{
    public function __construct(array $validated)
    {
        try {
            $reflectionClass = new \ReflectionClass(get_class($this));
            $properties = $reflectionClass->getProperties();
            foreach ($properties as $property) {
                $name = $property->getName();
                if (array_key_exists($name, $validated)) {
                    $this->{$name} = $validated[$name];
                }
            }
        } catch (\ReflectionException $exception) {
            throw new \Exception($exception->getMessage());
        }
    }

    /**
     * 小驼峰转下划线
     * @param $str
     */
//    private function toUnderScore($str): string
//    {
//        $dstr = preg_replace_callback('/([A-Z]+)/', function ($matchs) {
//            return '_' . strtolower($matchs[0]);
//        }, $str);
//        return trim(preg_replace('/_{2,}/', '_', $dstr), '_');
//    }
}
