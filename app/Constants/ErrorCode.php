<?php

declare(strict_types=1);
/**
 * API Error Codes
 *
 * This file defines all error codes used in the API
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

namespace App\Constants;

use Hyperf\Constants\AbstractConstants;
use Hyperf\Constants\Annotation\Constants;

#[Constants]
class ErrorCode extends AbstractConstants
{
    /**
     * @Message("Server Error")
     */
    public const SERVER_ERROR = 500;

    /**
     * @Message("Bad Request")
     */
    public const BAD_REQUEST = 400;

    /**
     * @Message("Unauthorized")
     */
    public const UNAUTHORIZED = 401;

    /**
     * @Message("Forbidden")
     */
    public const FORBIDDEN = 403;

    /**
     * @Message("Not Found")
     */
    public const NOT_FOUND = 404;

    /**
     * @Message("Method Not Allowed")
     */
    public const METHOD_NOT_ALLOWED = 405;

    /**
     * @Message("Validation Failed")
     */
    public const VALIDATION_ERROR = 422;

    /**
     * @Message("Too Many Requests")
     */
    public const TOO_MANY_REQUESTS = 429;

    /**
     * @Message("Invalid API Key")
     */
    public const INVALID_API_KEY = 1001;

    /**
     * @Message("API Key Expired")
     */
    public const API_KEY_EXPIRED = 1002;

    /**
     * @Message("API Rate Limit Exceeded")
     */
    public const API_RATE_LIMIT = 1003;
}
