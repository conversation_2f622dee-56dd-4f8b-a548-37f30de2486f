<?php
declare(strict_types=1);

namespace App\Constants;


use Hyperf\Constants\AbstractConstants;

class ResponseExceptionCode extends AbstractConstants
{
    const PARAM_ERROR_HTTP_CODE = 400;
    const PARAM_ERROR_CODE = 'PARAM_ERROR';
    const PARAM_ERROR_MESSAGE = '参数错误';

    const TOKEN_ERROR_HTTP_CODE = 403;
    const TOKEN_ERROR_CODE = 'TOKEN_INVALID';
    const TOKEN_ERROR_MESSAGE = 'token无效';

    const AK_SK_ERROR_HTTP_CODE = 401;
    const AK_SK_ERROR_CODE = 'NOT_PERMISSION';
    const AK_SK_ERROR_MESSAGE = '认证失败';

    const PERMISSION_ERROR_HTTP_CODE = 403;
    const PERMISSION_ERROR_CODE = 'PERMISSION_DENIED';
    const PERMISSION_ERROR_MESSAGE = '没有权限';

    const APPID_ERROR_HTTP_CODE = 403;
    const APPID_ERROR_CODE = 'APPID_INVALID';
    const APPID_ERROR_MESSAGE = 'appID无效';

    const SUCCESS_CODE = 200;
}
