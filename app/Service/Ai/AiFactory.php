<?php

namespace App\Service\Ai;

use App\Service\Ai\DouBao\DouBaoService;
use function Hyperf\Support\make;

class AiFactory
{
    /**
     * @param string $type
     *
     * 'dou_bao', 'kimi', 'tong_yi', 'wen_xin', 'ke_da'
     */
    public function service(string $type)
    {
        return match ($type) {
            'dou_bao' => make(DouBaoService::class),
            default => null,
        };
    }
}
