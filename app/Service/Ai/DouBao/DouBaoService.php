<?php

namespace App\Service\Ai\DouBao;

use App\Constants\ContextConst;
use App\Service\Ai\AiInterface;
use GuzzleHttp\Exception\GuzzleException;
use Hyperf\Context\Context;
use Hyperf\Guzzle\ClientFactory;
use Psr\Log\LoggerInterface;

class DouBaoService implements AiInterface
{
    protected string $model;
    protected string $token;

    protected string $baseUri = 'https://ark.cn-beijing.volces.com';

    protected ClientFactory $clientFactory;
    protected LoggerInterface $logger;
    public function __construct(ClientFactory $clientFactory, LoggerInterface $logger)
    {
        $this->model = Context::get(ContextConst::AI_MODEL);
        $this->token = Context::get(ContextConst::AI_TOKEN);
        $this->clientFactory = $clientFactory;
        $this->logger = $logger;
    }

    /**
     * AI Chat
     * curl https://ark.cn-beijing.volces.com/api/v3/chat/completions \
     * -H "Content-Type: application/json" \
     * -H "Authorization: Bearer 9447322d-6526-452a-9208-0d5d526a0c5f" \
     * -d '{
     * "model": "doubao-1-5-thinking-pro-250415",
     * "messages": [
     * {"role": "system","content": "你是人工智能助手."},
     * {"role": "user","content": "你好"}
     * ]
     * }'
     *
     * {"choices":[{"finish_reason":"stop","index":0,"logprobs":null,"message":{"content":"\n\n你好呀！有什么我可以帮忙的吗？无论是问题咨询、聊天还是其他需求，都可以跟我说哦～","reasoning_content":"好的，用户现在给了我一个问候“你好”，我需要回应。首先，用户可能只是想打个招呼，所以应该友好回应。我应该保持亲切自然的语气，让用户感到被欢迎。可以简单回复“你好呀！有什么我可以帮忙的吗？”这样既回应了问候，又邀请用户进一步交流。不需要太复杂，保持简洁和温暖。检查一下有没有错别字，确认语气合适。然后就可以回复了。\n","role":"assistant"}}],"created":1748533048,"id":"021748533043137ede98e0f428e8900e0a9dc120396bad060ef96","model":"doubao-1-5-thinking-pro-250415","service_tier":"default","object":"chat.completion","usage":{"completion_tokens":131,"prompt_tokens":22,"total_tokens":153,"prompt_tokens_details":{"cached_tokens":0},"completion_tokens_details":{"reasoning_tokens":103}}}
     * @param string $message
     */
    public function chatCompletions(string $message, string $systemRule = '')
    {

        $client = $this->clientFactory->create([
            'headers' => [
                'Authorization' => 'Bearer ' . $this->token,
                'Content-Type' => 'application/json'
            ]
        ]);
        try {
            $response = $client->request('POST', $this->baseUri . '/api/v3/chat/completions', [
                'json' => [
                    'model' => $this->model,
                    'messages' => [
                        [
                            'role' => 'system',
                            'content' => $systemRule ?? '你是经验丰富的中西医结合医疗专家'
                        ],
                        [
                            'role' => 'user',
                            'content' => $message
                        ]
                    ],
                    'temperature' => 0.6, // 降低随机性，加快响应
                    'max_tokens' => 5000, // 限制生成长度，按需调整
                ],
                'timeout' => 120
            ]);
            return json_decode($response->getBody()->getContents(), true);
        } catch (\Exception|GuzzleException $e) {
            $this->logger->error('【DouBao AI】error:' . $e->getFile() . '-' . $e->getLine() . '-' . $e->getMessage());
            throw new \Exception('AI Chat Error : ' . $e->getMessage());
        }
    }

    public function buildReport()
    {
        // TODO: Implement buildReport() method.
    }
}
