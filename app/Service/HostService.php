<?php

namespace App\Service;

use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Server\ServerInterface;
use function Hyperf\Config\config;

class HostService
{
    public function getHost(RequestInterface $request): string
    {
        $hostHeader = $request->header('host');

        if (empty($hostHeader)) {
            // 当 Host 头不存在时，获取服务器配置的地址
            return $this->getServerAddress($request);
        }

        $hostParts = explode(':', $hostHeader, 2);
        $hostname = $hostParts[0];
        $port = $hostParts[1] ?? null;

        // 判断是否为 IP 地址
        if (filter_var($hostname, FILTER_VALIDATE_IP)) {
            return $this->formatIpWithPort($hostname, $port, $request->getUri()->getScheme());
        }
        // 直接返回 Host 头内容（含域名和端口）
        return $request->getUri()->getScheme() . '://' . $hostHeader;
    }

    protected function getServerAddress(RequestInterface $request): string
    {
        $serverConfig = config('server.servers', []);

        // 使用原生数组遍历替代 collect()
        $httpServer = null;
        foreach ($serverConfig as $server) {
            $type = $server['type'] ?? '';
            if ($type === ServerInterface::SERVER_HTTP) {
                $httpServer = $server;
                break;
            }
        }

        $host = $httpServer['host'] ?? '0.0.0.0';
        $port = $httpServer['port'] ?? 9501;
        $scheme = $request->getUri()->getScheme();

        return $this->formatIpWithPort($host, $port, $scheme);
    }

    protected function formatIpWithPort(string $host, ?string $port, string $scheme): string
    {
        $defaultPort = $scheme === 'https' ? 443 : 80;
        $port = $port ? (int)$port : $defaultPort;

        if ($port !== $defaultPort) return "{$scheme}://{$host}:{$port}";

        return $scheme . '://' . $host;
    }
}
