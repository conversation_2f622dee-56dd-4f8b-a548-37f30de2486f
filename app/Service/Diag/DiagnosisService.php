<?php

namespace App\Service\Diag;

use App\Constants\ContextConst;
use GuzzleHttp\Exception\GuzzleException;
use Hyperf\Context\Context;
use Hyperf\Guzzle\ClientFactory;
use Psr\Log\LoggerInterface;

class DiagnosisService
{
    protected string $host = 'https://ali-market-tongue-detect-v2.macrocura.com';

    protected string $appCode;

    protected ClientFactory $clientFactory;
    protected LoggerInterface $logger;

    public function __construct(
        ClientFactory   $clientFactory,
        LoggerInterface $logger
    )
    {
        $this->logger = $logger;
        $this->clientFactory = $clientFactory;
        $this->appCode = Context::get(ContextConst::APP_CODE);
    }

    /**
     * 脸舌诊
     * 创建本次⾆⾯诊断2.0的会话 or 直接⽣成报告（通过scene参数控制）
     *  上传⾆照、⾯照、⾆下静脉照图⽚，系统会⾃动识别显⽰相应结果，同时系统还会提供简单选
     * 项，根据实际情况回答后即可继续解锁健康报告和调理⽅案
     * @param array $data
     * @return array
     * {
     * "scene": 1,
     * // tf_image、ff_image、tb_image 这三个字段至少要填一个
     * "ff_image": "https://xxxx.jpg",
     * "tf_image": "https://xxxx.jpg",
     * "tb_image": "https://xxxx.jpg",
     * "gender": "男"
     * }
     * @throws \Exception
     */
    public function faceDiagnosis(array $data): array
    {
        $path = '/diagnose/face-tongue/result/';
        $params = [
            'scene' => (string)$data['scene'] ?? '1', // 1: 推荐，问答获取报告 2: 直接根据图片获取报告
            'ff_image' => $data['ffImage'] ?? '',  // 舌照图片
            'tf_image' => $data['tfImage'] ?? '',  // 面照图片
            'tb_image' => $data['tbImage'] ?? '',  // 舌下脉络图片
            'gender' => $data['gender'] // 性别
        ];
        return $this->diagnosisRequest($path, $params);
    }

    /**
     * 获取报告
     * 回答问诊问题，从⽽获取报告
     * @throws \Exception
     * {
     * "session_id": "84e70922-489d-11ef-84dc-00163e25c208",
     * "answers": [
     * {
     * "name": "面色晦暗",
     * "value": "6c097f16-31b8-49f3-8988-f929d16fbf14"
     * }
     * ]
     * }
     */
    public function diagnosisReport(array $data)
    {
        $path = '/diagnose/face-tongue/report/';
        $params = [
            'session_id' => $data['sessionId'] ?? '',
            'answers' => json_decode($data['answers'], true) ?? [] // 传 json 字符串解析
        ];
        return $this->diagnosisRequest($path, $params);
    }

    /**
     * 诊断请求
     * @param string $method
     * @param string $path
     * @param array $params
     * @return mixed
     * @throws \Exception
     */
    protected function diagnosisRequest(string $path, array $params = []): mixed
    {
        try {
            $response = $this->clientFactory->create([
                'base_uri' => $this->host,
                'headers' => [
                    'Authorization' => 'APPCODE ' . $this->appCode,
                    'Content-Type' => 'application/json; charset=UTF-8'
                ]
            ])->request('POST', $path, [
                'json' => $params
            ]);

            $result = json_decode($response->getBody()->getContents(), true);
            if ($result['success'] === false) {
                $this->logger->error('DIAG API Error Response', [
                    'params' => $params,
                    'response' => $result,
                ]);
                throw new \Exception($result['msg']);
            }
            $this->logger->info('DIAG API Info Response', [
                'params' => $params,
                'response' => json_encode($result, JSON_UNESCAPED_UNICODE),
            ]);
            return $result;
        } catch (GuzzleException|\Exception|\Throwable $e) {
            $this->logger->error('DIAG API Exception', [
                'params' => $params,
                'exception' => [
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTrace()
                ]
            ]);
            throw new \Exception($e->getMessage());
        }
    }
}
