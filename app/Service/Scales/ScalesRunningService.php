<?php

namespace App\Service\Scales;

use App\Constants\ContextConst;
use App\Model\LyScalesReport;
use App\Service\Ai\AiFactory;
use Hyperf\Context\Context;
use Hyperf\Redis\Redis;
use function Hyperf\Support\make;

class ScalesRunningService
{
// 状态常量
    const STATUS_INIT = 'init';
    const STATUS_PHYSIOLOGY_RUNNING = 'physiology_running';
    const STATUS_PSYCHOLOGY_RUNNING = 'psychology_running';
    const STATUS_SOCIETY_RUNNING = 'society_running';
    const STATUS_SYNTHESIS_RUNNING = 'synthesis_running';
    const STATUS_FINISHED = 'finished';
    const STATUS_FAILED = 'fail';

    // 进度常量
    const PROGRESS_INIT = 0;
    const PROGRESS_ANSWER_CLASSIFIED = 10;
    const PROGRESS_PHYSIOLOGY_SCORE = 20;
    const PROGRESS_PHYSIOLOGY_REPORT = 30;
    const PROGRESS_PSYCHOLOGY_SCORE = 40;
    const PROGRESS_PSYCHOLOGY_REPORT = 50;
    const PROGRESS_SOCIETY_SCORE = 60;
    const PROGRESS_SOCIETY_REPORT = 70;
    const PROGRESS_SYNTHESIS_SCORE = 80;
    const PROGRESS_SYNTHESIS_REPORT = 90;
    const PROGRESS_COMPLETED = 100;

    protected Redis $redis;
    private int $defaultPass = 86400; // 60 * 60 * 24

    protected string $aiType;

    public function __construct(Redis $redis)
    {
        $this->redis = $redis;
    }

    public function getScalesReport(string $reportUuid): bool
    {
        $processStatusKey = $reportUuid . ':process';
        $taskInfo = $this->getTaskInfo($reportUuid);
        $service = make(CalculationService::class);

        try {
            // 处理生理维度
            $this->processDimension(
                $service,
                $taskInfo,
                $processStatusKey,
                1,
                'physiology',
                self::STATUS_PHYSIOLOGY_RUNNING,
                self::PROGRESS_ANSWER_CLASSIFIED,
                self::PROGRESS_PHYSIOLOGY_SCORE,
                self::PROGRESS_PHYSIOLOGY_REPORT
            );
            // 处理心理维度
            $this->processDimension(
                $service,
                $taskInfo,
                $processStatusKey,
                2,
                'psychology',
                self::STATUS_PSYCHOLOGY_RUNNING,
                self::PROGRESS_PHYSIOLOGY_REPORT,
                self::PROGRESS_PSYCHOLOGY_SCORE,
                self::PROGRESS_PSYCHOLOGY_REPORT
            );

            // 处理社会维度
            $this->processDimension(
                $service,
                $taskInfo,
                $processStatusKey,
                3,
                'society',
                self::STATUS_SOCIETY_RUNNING,
                self::PROGRESS_PSYCHOLOGY_REPORT,
                self::PROGRESS_SOCIETY_SCORE,
                self::PROGRESS_SOCIETY_REPORT
            );

            // 处理综合维度
            $this->processDimension(
                $service,
                $taskInfo,
                $processStatusKey,
                0,
                'synthesis',
                self::STATUS_SYNTHESIS_RUNNING,
                self::PROGRESS_SOCIETY_REPORT,
                self::PROGRESS_SYNTHESIS_SCORE,
                self::PROGRESS_SYNTHESIS_REPORT
            );

            // 完成处理
            $this->updateRedisStatus($processStatusKey, self::PROGRESS_COMPLETED, self::STATUS_FINISHED, '完成');

            $taskInfo->status = self::STATUS_FINISHED;
            $taskInfo->process = self::PROGRESS_COMPLETED;
            $taskInfo->save();

            $this->redis->del($processStatusKey);

            return true;
        } catch (\Exception $e) {
            $this->updateRedisStatus(
                $processStatusKey,
                $taskInfo->process ?? self::PROGRESS_INIT,
                self::STATUS_FAILED,
                '处理失败: ' . $e->getMessage()
            );

            if ($taskInfo) {
                $taskInfo->status = self::STATUS_FAILED;
                $taskInfo->save();
            }

            throw $e;
        }
    }

    private function getTaskInfo(string $reportUuid)
    {
        $model = new LyScalesReport();
        $taskInfo = $model->where('report_uuid', $reportUuid)->first();

        if (!$taskInfo) {
            throw new \Exception('任务不存在');
        }

        return $taskInfo;
    }

    /**
     * @throws \Exception
     */
    private function processDimension(
        CalculationService $service,
                           $taskInfo,
        string             $processStatusKey,
        int                $dimension,
        string             $dimensionName,
        string             $status,
        int                $startProgress,
        int                $scoreProgress,
        int                $reportProgress
    ): void
    {
        // 更新开始状态
        $this->updateRedisStatus($processStatusKey, $startProgress, $status, "{$dimensionName}维度计算中");

        // 计算得分
        $score = $service
            ->setReportUuid($taskInfo->report_uuid)
            ->setAge($taskInfo->age)
            ->setSex($taskInfo->gender)
            ->setDimension($dimension)
            ->setAnswer($taskInfo->answer)
            ->score();
        // 保存得分
        $taskInfo->{"{$dimensionName}_score"} = $score['score'];
        $taskInfo->{"{$dimensionName}_diagnosis"} = $score['diagnosis'];
        $taskInfo->save();

        // 更新得分状态
        $this->updateRedisStatus($processStatusKey, $scoreProgress, $status, "{$dimensionName}维度报告生成中");

        // 获取并保存报告
        $report = $this->getReport($score['prompt']);
        $taskInfo->{"{$dimensionName}_report"} = $report;
        $taskInfo->save();

        // 更新报告完成状态
        $this->updateRedisStatus($processStatusKey, $reportProgress, $status, "{$dimensionName}维度处理完成");
    }

    private function updateRedisStatus(
        string $key,
        int    $progress,
        string $status,
        string $message
    ): void
    {
        $this->redis->set(
            $key,
            json_encode([
                'process' => $progress,
                'status' => $status,
                'momo' => $message
            ]),
            $this->defaultPass
        );
    }

    /**
     * @throws \Exception
     */
    public function getReport(string $prompt)
    {
        $this->aiType = Context::get(ContextConst::AI_TYPE) ?? 'dou_bao';
        $aiFactory = make(AiFactory::class)->service($this->aiType);
        $res = $aiFactory->chatCompletions($prompt);
        if ($res['code'] == 200 && $res['data']['choices'][0]['finish_reason'] == 'stop') {
            return $res['data']['choices'][0]['message']['content'];
        }
        return 'error';
    }
}
