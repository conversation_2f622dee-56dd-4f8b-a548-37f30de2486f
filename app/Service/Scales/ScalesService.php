<?php

namespace App\Service\Scales;

use App\Constants\ContextConst;
use App\Model\LyScale;
use App\Model\LyScalesReport;
use Hyperf\Context\Context;
use Hyperf\Logger\LoggerFactory;
use Hyperf\Redis\Redis;
use Psr\Log\LoggerInterface;
use Ramsey\Uuid\Uuid;
use function Hyperf\Support\make;

class ScalesService
{
    protected Redis $redis;
    protected LoggerInterface $logger;
    protected string $ak;

    protected ScalesReportQueueService $queueService;

    public function __construct(Redis $redis, LoggerFactory $loggerFactory, ScalesReportQueueService $queueService)
    {
        $this->redis = $redis;
        $this->logger = $loggerFactory->get('log', 'default');
        $this->ak = Context::get(ContextConst::AK);
        $this->queueService = $queueService;
    }


    /**
     * 获取量表题目列表
     * @param int $dimension
     * @return array
     */
    public function getScalesQuestion(int $dimension = 0): array
    {
        $model = new LyScale();
        $where = ['status' => 1];

        if ($dimension > 0) {
            $where['dimension'] = $dimension;
        }

        $fields = ['id', 'serial_num', 'title', 'options', 'type', 'dimension'];
        $list = $model->where($where)
            ->select($fields)
            ->orderBy('serial_num')
            ->get();

        if ($list->isEmpty()) {
            return [];
        }

        $dimensionMap = [
            1 => ['key' => 'physiology', 'text' => '生理健康维度'],
            2 => ['key' => 'psychology', 'text' => '心理健康维度'],
            3 => ['key' => 'society', 'text' => '社会健康维度'],
        ];

        $result = [];
        $answerScore = [];

        foreach ($list as $item) {
            $dim = $item->dimension;
            if (isset($dimensionMap[$dim])) {
                $key = $dimensionMap[$dim]['key'];
                if (!isset($result[$key])) {
                    $result[$key] = [
                        'text' => $dimensionMap[$dim]['text'],
                        'questions' => []
                    ];
                }
                $result[$key]['questions'][] = $item;
            }
            $question = $item->options;
            $serialNum = $item->serial_num;
            if (is_array($question)) {
                foreach ($question as $vo) {
                    $answerScore[] = [
                        'key' => $vo['key'],
                        'score' => $vo['score'],
                        'dimension' => $dim,
                        'type' => $item->type,
                        'title' => $item->title,
                    ];
                }
            }
        }
        $redisKey = Uuid::uuid4()->toString();
        $this->writeListToRedis($redisKey, $answerScore);

        $result['report_uuid'] = $redisKey;
        return $result;
    }

    /**
     * 将一个列表写入到 Redis 中
     *
     * @param string $key Redis 中的键
     * @param array $list 要写入的列表数据
     */
    protected function writeListToRedis(string $key, array $list): void
    {
        // 清空 Redis 中的旧列表（如果存在）
        $this->redis->del($key);
        $indexKey = $key . ':index'; // 创建辅助索引

        // 将列表数据写入 Redis
        foreach ($list as $item) {
            // 将每个列表项序列化为 JSON 格式
            if (is_array($item)) {
//                $this->redis->zAdd($key, $item['score'], $item['key']);
                $id = $this->redis->xAdd($key, '*', [
                    'key' => $item['key'],
                    'score' => $item['score'],
                    'title' => $item['title'],
                    // 添加其他需要存储的字段
                    'dimension' => $item['dimension'] ?? null,
                    'type' => $item['type'] ?? null,
                ]);
                // 建立key到消息ID的映射
                $this->redis->hSet($indexKey, $item['key'], $id);
            }
        }

        $this->logger->info("列表已成功写入 Redis，键为: $key\n");
    }

    /**
     * 获取列表中指定元素的值
     * @param string $streamKey
     * @param string $itemKey
     * @return array|null
     */
    public function getByKey(string $streamKey, string $itemKey): ?array
    {
        $indexKey = $streamKey . ':index';
        $messageId = $this->redis->hGet($indexKey, $itemKey);

        if ($messageId) {
            return $this->redis->xRange($streamKey, $messageId, $messageId)[$messageId] ?? null;
        }

        return null;
    }

    /**
     * 投递获取报告异步任务
     * @param array $data
     * @return array
     */
    public function createScalesReportTask(array $data): array
    {
        // 获取特定元素的分数
//        $score = $this->redis->zScore('97daf2f2-ea3b-4dcc-ae3c-51591ff40091', '39_h7i8j9k0');
//        $res = $this->getByKey('ef85cf2e-48d6-4b42-a8a0-c6779999c03a', '39_h7i8j9k0');
        $model = new LyScalesReport();
        // [{"serial_num": 1, "key": "a1b2c3d4"，"question":"","label":""},{"serial_num": 1, "key": "a1b2c3d4"，"question":"","label":""}]
        $taskInfo = $model->where('report_uuid', $data['reportUuid'])->first();
        if ($taskInfo) {
            throw new \Exception('任务已存在，请勿重复提交');
        }
        $task = [
            'ak' => $this->ak,
            'report_uuid' => $data['reportUuid'],
            'openid' => $data['openid'],
            'gender' => $data['gender'],
            'age' => $data['age'],
            'answer' => $data['answer'],
            'status' => 'init',
            'process' => 0,
            'remark' => date('Y-m-d H:i:s') . '创建任务' . PHP_EOL,
            'status_memo' => '任务初始化'
        ];
        $rs = $model->create($task);
        // 投递异步任务
        $taskItem = [
            'id' => $rs->id,
            'report_uuid' => $task['report_uuid']
        ];
        $this->queueService->push(['reportUuid' => $taskItem['report_uuid']]);
        $this->logger->info("量表任务创建成功，键为: {$taskItem['report_uuid']}\n");
        return $taskItem;
    }


    /**
     * 获取任务状态
     * @param string $reportUuid
     * @return array
     * @throws \Exception
     */
    public function getScalesReportTaskStatus(string $reportUuid): array
    {
        // 验证任务
        $model = new LyScalesReport();
        $taskInfo = $model->where('report_uuid', $reportUuid)->first();
        if (!$taskInfo) {
            throw new \Exception('任务不存在');
        }
        $processStatusKey = $reportUuid . ':process';

        if ($this->redis->exists($processStatusKey)) {
            $cacheProcess = json_decode($this->redis->get($processStatusKey), true);
            $taskInfo->process = $cacheProcess['process'];
            $taskInfo->status = $cacheProcess['status'];
            $taskInfo->status_memo = $cacheProcess['momo'];
        }
        return [
            'process' => $taskInfo->process,
            'status' => $taskInfo->status,
            'report_uuid' => $taskInfo->report_uuid,
            'status_memo' => $taskInfo->status_memo,
            'id' => $taskInfo->id,
            'physiology_score' => $taskInfo->physiology_score ?? 0.00,
            'psychology_score' => $taskInfo->psychology_score ?? 0.00,
            'society_score' => $taskInfo->society_score ?? 0.00,
            'synthesis_score' => $taskInfo->synthesis_score ?? 0.00,
            'physiology_report' => $taskInfo->physiology_report ?? '',
            'psychology_report' => $taskInfo->psychology_report ?? '',
            'society_report' => $taskInfo->society_report ?? '',
            'synthesis_report' => $taskInfo->synthesis_report ?? '',
            'physiology_diagnosis' => $taskInfo->physiology_diagnosis ?? '',
            'psychology_diagnosis' => $taskInfo->psychology_diagnosis ?? '',
            'society_diagnosis' => $taskInfo->society_diagnosis ?? '',
            'synthesis_diagnosis' => $taskInfo->synthesis_diagnosis ?? '',
        ];
    }


    /**
     * @throws \Exception
     */
    public function getScalesReportTaskTest(string $reportUuid): bool
    {
        $runningService = make(ScalesRunningService::class);
        $res = $runningService->getScalesReport($reportUuid);
        return $res;
    }


    /**
     * @throws \Exception
     */
//    public function getScalesReport(string $reportUuid): bool
//    {
//        // 状态：Init:初始化（0），
//        //physiology_running:生理报告获取运行中（10-30），
//        //psychology_running：心理报告获取运行中（30-60），
//        //society_running:社会报告获取运行中（60-90），
//        //finished:任务完成（100）,
//        // fail:异常失败
//        // 进度定义：初始化 0，
//        //分类答案完成 10，
//        //生理得分获取完成 20，
//        //生理报告获取完成 30，
//        //心理得分获取完成 40，
//        //心理报告获取完成 50，
//        //社会得分获取完成 60，
//        //社会报告获取完成 70，
//        //综合得分获取完成 80，
//        //综合报告获取完成 90，
//        //任务完成 100
//        // 验证任务
//        $processStatusKey = $reportUuid . ':process';
//        $model = new LyScalesReport();
//        $taskInfo = $model->where('report_uuid', $reportUuid)->first();
//        if (!$taskInfo) {
//            throw new \Exception('任务不存在');
//        }
//        // 计算维度得分
//        $service = make(CalculationService::class);
//
//        // 1。 计算生理维度得分
//        $defaultPass = 60 * 60 * 24;
//        $this->redis->set($processStatusKey, json_encode(['process' => 10, 'status' => 'physiology_running', 'momo' => '生理维度计算中']), $defaultPass);
//        $physiologyScore = $service->setReportUuid($reportUuid)->setAge($taskInfo->age)->setSex($taskInfo->gender)->setDimension(1)->setAnswer($taskInfo->answer)->score();
//        $taskInfo->physiology_score = $physiologyScore['score'];
//        $taskInfo->physiology_diagnosis = $physiologyScore['diagnosis'];
//        $taskInfo->save();
//        // 获取报告
//        $this->redis->set($processStatusKey, json_encode(['process' => 20, 'status' => 'physiology_running', 'momo' => '生理维度报告生成中']), $defaultPass);
//        $physiologyReport = $this->getReport($physiologyScore['prompt']);
//        $taskInfo->physiology_report = $physiologyReport;
//        $taskInfo->save();
//        $this->redis->set($processStatusKey, json_encode(['process' => 30, 'status' => 'psychology_running', 'momo' => '心理维度计算中']), $defaultPass);
//        // 2。 计算心理维度得分
//        $psychologyScore = $service->setReportUuid($reportUuid)->setAge($taskInfo->age)->setSex($taskInfo->gender)->setDimension(2)->setAnswer($taskInfo->answer)->score();
//        $taskInfo->psychology_score = $psychologyScore['score'];
//        $taskInfo->psychology_diagnosis = $psychologyScore['diagnosis'];
//        $taskInfo->save();
//        // 获取报告
//        $this->redis->set($processStatusKey, json_encode(['process' => 40, 'status' => 'psychology_running', 'momo' => '心理维度报告生成中']), $defaultPass);
//        $psychologyReport = $this->getReport($psychologyScore['prompt']);
//        $taskInfo->psychology_report = $psychologyReport;
//        $taskInfo->save();
//
//        $this->redis->set($processStatusKey, json_encode(['process' => 50, 'status' => 'society_running', 'momo' => '社会维度计算中']), $defaultPass);
//        // 3。 计算社会维度得分
//        $societyScore = $service->setReportUuid($reportUuid)->setAge($taskInfo->age)->setSex($taskInfo->gender)->setDimension(3)->setAnswer($taskInfo->answer)->score();
//        $taskInfo->society_score = $societyScore['score'];
//        $taskInfo->society_diagnosis = $societyScore['diagnosis'];
//        $taskInfo->save();
//        // 获取报告
//        $this->redis->set($processStatusKey, json_encode(['process' => 60, 'status' => 'society_running', 'momo' => '社会维度报告生成中']), $defaultPass);
//        $societyReport = $this->getReport($societyScore['prompt']);
//        $taskInfo->society_report = $societyReport;
//        $taskInfo->save();
//
//        $this->redis->set($processStatusKey, json_encode(['process' => 70, 'status' => 'synthesis_running', 'momo' => '综合维度计算中']), $defaultPass);
//        // 4。 计算综合维度得分
//        $synthesisScore = $service->setReportUuid($reportUuid)->setAge($taskInfo->age)->setSex($taskInfo->gender)->setDimension(0)->setAnswer($taskInfo->answer)->score();
//        $taskInfo->synthesis_score = $synthesisScore['score'];
//        $taskInfo->synthesis_diagnosis = $synthesisScore['diagnosis'];
//        $taskInfo->save();
//        // 获取报告
//        $this->redis->set($processStatusKey, json_encode(['process' => 80, 'status' => 'synthesis_running', 'momo' => '综合维度报告生成中']), $defaultPass);
//        $synthesisReport = $this->getReport($synthesisScore['prompt']);
//        $taskInfo->synthesis_report = $synthesisReport;
//        $taskInfo->save();
//        $this->redis->set($processStatusKey, json_encode(['process' => 90, 'status' => 'synthesis_running', 'momo' => '报告整理中']), $defaultPass);
//        // 完成
//        $this->redis->set($processStatusKey, json_encode(['process' => 100, 'status' => 'finished', 'momo' => '完成']), $defaultPass);
//        $taskInfo->status = 'finished';
//        $taskInfo->process = 100;
//        $taskInfo->save();
//        $this->redis->del($processStatusKey);
//        return true;
//    }


}
