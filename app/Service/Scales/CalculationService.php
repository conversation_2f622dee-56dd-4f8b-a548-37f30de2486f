<?php

namespace App\Service\Scales;

use Hyperf\Redis\Redis;

class CalculationService
{
    /**
     * 年龄
     * @var int
     */
    public int $age;
    /**
     * 性别
     * @var string
     */
    public string $sex;
    /**
     * 参与计算的答案
     * @var array
     */
    public array $calculationAnswers;
    /**
     * 报告UUID
     * @var string
     */
    public string $reportUuid;
    /**
     * 计算类型
     * @var int
     * 0=综合 1=生理健康，2=心理健康，3=社会健康
     */
    public int $dimension = -1;

    public array $config;

    protected Redis $redis;
    protected ScalesService $scalesService;

    public function __construct(Redis $redis, ScalesService $scalesService)
    {
        $this->redis = $redis;
        $this->scalesService = $scalesService;
    }


    /**
     * 得分
     *
     */
    public function score(): array
    {
        // 维度得分=（维度原始得分—维度理论最低分）/（维度理论最高分—维度理论最低分）*100
        $originalScore = $this->originalScore();
        $score = round(($originalScore - $this->config['miniScore']) / ($this->config['maxScore'] - $this->config['miniScore']) * 100, 2);
        $diagnosis = $this->modalDiagnosis($score);
        $prompt = $this->reportPrompt($diagnosis);
        return [
            'originalScore' => $originalScore,
            'score' => $score,
            'diagnosis' => $diagnosis,
            'prompt' => $prompt,
        ];
    }

    /**
     *   生成报告提示词
     */
    public function reportPrompt(string $diagnosis = ''): string
    {
        // 问题及答案
        $questionAnswer = $this->getQuestionAnswer();
        $title = $this->config['title'];
        $require = $this->config['require'];
        return <<<EOF
请根据以下提供的信息，生成一份医学量表测评报告。
用户回答的问题记答案：{$questionAnswer},年龄{$this->age},性别{$this->sex},
这是{$title}的报告要求,{$require},诊断为{$diagnosis}。
报告内容格式如下：
• 测评维度：明确指出当前分析的维度（生理、心理、社会或综合）。
• 健康分析：详细阐述该维度的健康状况，包括问题分析、指标解读等。
• 健康建议：根据分析结果，提出具体的改进建议。
请确保报告内容科学、准确、具有可操作性，语言简洁明了，便于用户理解。
EOF;
    }

    /**
     * 设定参与计算的答案
     */
    public function setAnswer(string $answers): static
    {
        $this->getConfig();
        $answers = json_decode($answers, true);
        foreach ($answers as $item) {
            if (in_array((int)$item['serial_num'], array_merge($this->config['serialNums'], $this->config['reverseNums'], $this->config['ignore']))) {
                $this->calculationAnswers[] = $item;
            }
        }
        return $this;
    }

    public function setAge(int $age): static
    {
        $this->age = $age;
        return $this;
    }

    public function setSex(string $sex): static
    {
        $this->sex = $sex;
        return $this;
    }

    public function setReportUuid(string $reportUuid): static
    {
        $this->reportUuid = $reportUuid;
        return $this;
    }

    public function setDimension(int $dimension): static
    {
        $this->dimension = $dimension;
        return $this;
    }

    public function getConfig(): array
    {
        // 0=综合 1=生理健康，2=心理健康，3=社会健康
        $config = [
            0 => [
                'title' => '综合健康测评维度',
                'maxScore' => 175,
                'miniScore' => 35,
                'serialNums' => [
                    1, 2, 3, 4, 5, 6, 7, 8, 9, 10,
                    11, 12, 13, 14, 15, 16, 17, 18,
                    19, 20, 21, 22, 23, 24, 25, 26,
                    27, 28, 29, 30, 31, 32, 33, 34,
                    35, 36, 37, 38, 39
                ],
                'reverseNums' => [4, 5, 6, 7, 8, 9, 10, 11, 12, 20, 21, 22, 23, 24, 25],
                'ignore' => [15, 28, 38, 39],
                'male' => [
                    [[0, 58], [58, 63.91], [63.91, 75.72], [75.72, 81.62], [81.62, 100]],       //<30
                    [[0, 56.88], [56.88, 63.25], [63.25, 75.98], [75.98, 82.34], [82.34, 100]], //>=30,<=50
                    [[0, 54.17], [54.17, 60.31], [60.31, 72.59], [72.59, 78.73], [78.73, 100]], //>50
                ],
                'female' => [
                    [[0, 57.09], [57.09, 62.92], [62.92, 74.57], [74.57, 80.39], [80.39, 100]],       //<30
                    [[0, 55.67], [55.67, 61.56], [61.56, 73.33], [73.33, 79.39], [79.21, 100]], //>=30,<=50
                    [[0, 53.12], [53.12, 59.17], [59.17, 71.26], [71.26, 77.30], [77.30, 100]], //>50
                ],
                'diagnosis' => ['疾病', '重度亚健康', '中度亚健康', '轻度亚健康', '健康'],
                'require' => '• 综合生理、心理、社会三个维度的测评结果，进行整体健康状况的总结。• 提供健康问题总结，明确指出用户在各维度中存在的主要问题及其相互关联。• 提出综合性的健康建议，涵盖生理、心理、社会三个方面的改善措施，帮助用户全面提升健康水平。'
            ],
            1 => [
                'title' => '生理健康测评维度',
                'maxScore' => 70,
                'miniScore' => 14,
                'serialNums' => [1, 2, 3, 13, 14],
                'reverseNums' => [4, 5, 6, 7, 8, 9, 10, 11, 12],
                'ignore' => [15],
                'male' => [
                    [[0, 60.08], [60.08, 66.8], [66.8, 80.23], [80.23, 86.94], [86.94, 100]],       //<30
                    [[0, 63.97], [63.97, 69.83], [69.83, 81.55], [81.55, 87.41], [87.41, 100]], //>=30,<=50
                    [[0, 56.49], [56.49, 63.26], [63.26, 76.8], [76.8, 83.57], [83.57, 100]], //>50
                ],
                'female' => [
                    [[0, 58.69], [58.69, 64.97], [64.97, 77.53], [77.53, 83.81], [83.81, 100]],       //<30
                    [[0, 62.08], [62.08, 68.10], [68.10, 80.14], [80.14, 86.16], [86.16, 100]], //>=30,<=50
                    [[0, 54.4], [54.4, 61.14], [61.14, 74.62], [74.62, 81.36], [81.36, 100]], //>50
                ],
                'diagnosis' => ['疾病', '重度亚健康', '中度亚健康', '轻度亚健康', '健康'],
                'require' => '• 根据用户在生理相关问题上的回答，分析其生理健康状况。• 提供详细的健康分析，包括可能存在的生理问题、健康指标的解读等。• 提出针对性的健康建议，如饮食调整、运动建议、就医提示等。'
            ],
            2 => [
                'title' => '心理健康测评维度',
                'maxScore' => 60,
                'miniScore' => 12,
                'serialNums' => [16, 17, 18, 19, 26, 27],
                'reverseNums' => [20, 21, 22, 23, 24, 25],
                'ignore' => [28],
                'male' => [
                    [[0, 51.8], [51.8, 59.23], [59.23, 74.08], [74.08, 81.5], [81.5, 100]],       //<30
                    [[0, 53.06], [53.06, 60.86], [60.86, 76.46], [76.46, 84.26], [84.26, 100]], //>=30,<=50
                    [[0, 50.99], [50.99, 58.56], [58.56, 73.7], [73.7, 81.27], [81.27, 100]] //>50
                ],
                'female' => [
                    [[0, 50.51], [50.51, 57.68], [57.68, 72], [72, 79.17], [79.17, 100]],       //<30
                    [[0, 51.31], [51.31, 58.48], [58.48, 72.81], [72.81, 79.97], [79.97, 100]], //>=30,<=50
                    [[0, 49.58], [49.58, 56.83], [56.83, 71.32], [71.32, 78.56], [78.56, 100]]  //>50
                ],
                'diagnosis' => ['疾病', '重度亚健康', '中度亚健康', '轻度亚健康', '健康'],
                'require' => '• 根据用户在心理相关问题上的回答，分析其心理健康状况。• 提供详细的健康分析，包括情绪状态、压力水平、心理问题的初步判断等。• 提出针对性的健康建议，如情绪调节方法、心理疏导建议、是否需要专业心理干预等。'
            ],
            3 => [
                'title' => '社会健康测评维度',
                'maxScore' => 45,
                'miniScore' => 9,
                'serialNums' => [29, 30, 31, 32, 33, 34, 35, 36, 37],
                'reverseNums' => [],
                'ignore' => [38, 39],
                'male' => [
                    [[0, 48.46], [48.46, 56.67], [56.67, 73.08], [73.08, 81.28], [81.28, 100]],       //<30
                    [[0, 48.57], [48.57, 56.7], [56.7, 72.96], [72.96, 81.09], [81.09, 100]], //>=30,<=50
                    [[0, 44.81], [44.81, 53.06], [53.06, 69.56], [69.56, 77.81], [77.81, 100]], //>50
                ],
                'female' => [
                    [[0, 50.46], [50.46, 58], [58, 73.1], [73.1, 80.64], [80.64, 100]],       //<30
                    [[0, 48.85], [48.85, 56.39], [56.39, 71.46], [71.46, 78.99], [78.99, 100]], //>=30,<=50
                    [[0, 46.69], [46.69, 54.64], [54.64, 70.53], [70.53, 78.47], [78.47, 100]] //>50
                ],
                'diagnosis' => ['疾病', '重度亚健康', '中度亚健康', '轻度亚健康', '健康'],
                'require' => '• 根据用户在社会相关问题上的回答，分析其社会适应能力。• 提供详细的健康分析，包括人际关系、社会支持系统、生活压力应对能力等。• 提出针对性的健康建议，如改善人际关系的方法、增强社会支持的途径、应对生活压力的策略等。'
            ],
        ];
        $this->config = $config[$this->dimension];
        return $this->config;
    }

    /**
     * 原始得分
     *
     */
    private function originalScore(): int
    {
        $score = 0;
        foreach ($this->calculationAnswers as $item) {
            if (in_array($item['serial_num'], $this->config['ignore'])) {
                continue;
            }
            $cacheScore = $this->scalesService->getByKey($this->reportUuid, $item['key']);
            if (empty($cacheScore)) {
                continue;
            }
            // 正向得分
            if (in_array($item['serial_num'], $this->config['serialNums'])) {
                $score += $cacheScore['score'];
            }
            // 反向得分
            if (in_array($item['serial_num'], $this->config['reverseNums'])) {
                $score += (6 - $cacheScore['score']);
            }
        }
        return min([max([$score, $this->config['miniScore']]), $this->config['maxScore']]);
    }

    private function modalDiagnosis(float $score)
    {
        $sex = $this->sex == '男' ? 'male' : 'female';
        $age = match (true) {
            $this->age < 30 => 0,
            $this->age >= 30 && $this->age <= 50 => 1,
            $this->age > 50 => 2,
        };
        $line = $this->config[$sex][$age];
        foreach ($line as $key => $item) {
            if ($score >= $item[0] && $score < $item[1]) {
                return $this->config['diagnosis'][$key];
            }
        }
        return '未知';
    }

    /**
     * 获取用户在对应的题号选择的答案
     * @return string
     */
    private function getQuestionAnswer(): string
    {
        $answers = $this->calculationAnswers;
        $questionAnswer = '';
        foreach ($answers as $item) {
            $question = $item['question'];
            $questionAnswer .= '问题'.$item['serial_num'].':' . $question . '答案：' . $item['label'] . PHP_EOL;
        }
        return $questionAnswer;
    }

}
