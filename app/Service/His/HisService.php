<?php

namespace App\Service\His;

use App\Constants\ContextConst;
use App\Constants\Entity\AppointmentRegistrationEntity;
use App\Constants\Entity\PatientSaveEntity;
use GuzzleHttp\Client;
use Guz<PERSON>Http\Exception\GuzzleException;
use GuzzleHttp\TransferStats;
use Hyperf\Context\Context;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Logger\LoggerFactory;
use Hyperf\Redis\Redis;
use League\Flysystem\Filesystem;
use Psr\Log\LoggerInterface;
use function Hyperf\Support\make;

class HisService
{
    protected string $curl = '/w/control/ehis.jl公众号Stub.交易业务Stub/WX_Handle';
    protected string $jurl = '/w/control';
    public string $companyPartyId;
    public string $configId;
    public string $domain;
    public string $version;
    protected Redis $redis;
    protected LoggerInterface $logger;
    protected RequestInterface $request;
    protected Filesystem $filesystem;

    public function __construct(
        LoggerFactory    $loggerFactory,
        Redis            $redis,
        RequestInterface $request,
        Filesystem       $filesystem
    )
    {
        $this->logger = $loggerFactory->get('log', 'default');
        $this->redis = $redis;
        $this->request = $request;
        $this->filesystem = $filesystem;
        $this->companyPartyId = Context::get(ContextConst::COMPANY_PARTY_ID);
        $this->configId = Context::get(ContextConst::CONFIG_ID);
        $this->domain = Context::get(ContextConst::DOMAIN);
        $this->version = Context::get(ContextConst::VERSION);
    }


    /**
     * 1. 保存、编辑就诊人
     * @param PatientSaveEntity $patientSaveEntity
     * @return mixed
     * @throws \Exception
     */
    public function savePatient(PatientSaveEntity $patientSaveEntity): mixed
    {
        $handleName = 'bmfw.patient.info.store';
        $params = [
            'handleName' => $handleName,
            'companyPartyId' => $this->companyPartyId,
            'configId' => $this->configId,
            'idCardNo' => $patientSaveEntity->idCardNo,
            'name' => $patientSaveEntity->name,
            'address' => $patientSaveEntity->address,
            'sexTypeLabel' => $patientSaveEntity->sexTypeLabel,
            'phoneNumber' => $patientSaveEntity->phoneNumber,
            'birthday' => $patientSaveEntity->birthday,
            'isChild' => $patientSaveEntity->isChild,
            'nationLabel' => $patientSaveEntity->nationLabel,
            'openid' => $patientSaveEntity->openid,
        ];
        if ($patientSaveEntity->cardNo) {
            $params['cardNo'] = $patientSaveEntity->cardNo;
        }
        if ($patientSaveEntity->isChild == 'Y') {
            $params['linkType'] = $patientSaveEntity->linkType;
            $params['jhridNumber'] = $patientSaveEntity->jhridNumber;
            $params['jhrPhone'] = $patientSaveEntity->jhrPhone;
            $params['jhrname'] = $patientSaveEntity->jhrname;
        }
        return $this->hisRequest('POST', $params);
    }

    /**
     * 2. 查询就诊人信息
     * @param string $cardNo
     * @return mixed
     * @throws \Exception
     */
    public function getPatientInfo(string $cardNo): mixed
    {
        $handleName = 'bmfw.patient.info';
        $params = [
            'handleName' => $handleName,
            'companyPartyId' => $this->companyPartyId,
            'configId' => $this->configId,
            'cardNo' => $cardNo,
        ];

        return $this->hisRequest('POST', $params);
    }

    /**
     * 3. 查询预约当天可选择的科室列表
     * @return mixed
     * @throws \Exception
     */
    public function getDepartmentList(): mixed
    {
        $handleName = 'bmfw.appt.regist.dept';
        $params = [
            'handleName' => $handleName,
            'companyPartyId' => $this->companyPartyId,
            'configId' => $this->configId,
        ];
        $requestResult = $this->hisRequest('POST', $params);
        // 处理科室图标
        if (isset($requestResult['deptList'])) {
            $requestResult = $this->actionDepartmentPicture($requestResult);
        }
        return $requestResult;
    }

    /**
     * 4. 查询选择科室的可预约的医生列表
     * @throws \Exception
     */
    public function getDoctorList(string $deptId, string $startTime = '')
    {
        $handleName = 'bmfw.appt.regist.doctor.list';
        $params = [
            'handleName' => $handleName,
            'companyPartyId' => $this->companyPartyId,
            'configId' => $this->configId,
            'deptId' => $deptId,
            'startTime' => $startTime ?? date('Y-m-d')
        ];
        $requestResult = $this->hisRequest('POST', $params);
        // 处理医生头像
        if (isset($requestResult['doctorList'])) {
            $requestResult = $this->actionDoctorAvatar($requestResult, $deptId);
        }
        return $requestResult;
    }

    /**
     * 5. 查询医生排班列表
     * @throws \Exception
     */
    public function getDoctorSchedulingList(string $deptId, string $doctorCd, string $startTime = '')
    {
        $handleName = 'bmfw.appt.regist.doctor.appt';
        $params = [
            'handleName' => $handleName,
            'companyPartyId' => $this->companyPartyId,
            'configId' => $this->configId,
            'deptId' => $deptId,
            'startTime' => $startTime ?? date('Y-m-d'),
            'doctorCd' => $doctorCd
        ];
        $doctorScheduling = $this->hisRequest('POST', $params);
        if (isset($doctorScheduling['docInfo']['headImage'])) {
            $service = make(HisImageService::class);
            $doctorScheduling['docInfo']['headImage'] = $service->getDoctorHeadImage($this->companyPartyId, $deptId, $doctorCd, $doctorScheduling['docInfo']['headImage'], $doctorScheduling['docInfo']['doctorName']);
        }
        return $doctorScheduling;
    }

    /**
     * 6. 预约挂号
     * @param AppointmentRegistrationEntity $appointmentRegistrationEntity
     * @return mixed
     * @throws \Exception
     */
    public function appointmentRegistration(AppointmentRegistrationEntity $appointmentRegistrationEntity): mixed
    {
        $handleName = 'bmfw.appt.regist.store';
        $params = [
            'handleName' => $handleName,
            'companyPartyId' => $this->companyPartyId,
            'configId' => $this->configId,
            'deptId' => $appointmentRegistrationEntity->deptId,
            'startTime' => $appointmentRegistrationEntity->startTime,
            'planNumUuid' => $appointmentRegistrationEntity->planNumUuid,
            'cardNo' => $appointmentRegistrationEntity->cardNo,
            'idCardNo' => $appointmentRegistrationEntity->idCardNo,
            'patiName' => $appointmentRegistrationEntity->patiName,
            'phone' => $appointmentRegistrationEntity->phone,
            'startPeriod' => $appointmentRegistrationEntity->startPeriod,
            'endPeriod' => $appointmentRegistrationEntity->endPeriod,
        ];
        return $this->hisRequest('POST', $params);
    }

    /**
     * 12. 取消预约
     * @param string $cardNo
     * @param string $sarid
     * @return mixed
     * @throws \Exception
     */
    public function registrationCancel(string $cardNo, string $sarid): mixed
    {
        $handleName = 'bmfw.appt.regist.cancle';
        $params = [
            'handleName' => $handleName,
            'companyPartyId' => $this->companyPartyId,
            'configId' => $this->configId,
            'cardNo' => $cardNo,
            'sarid' => $sarid,
        ];
        return $this->hisRequest('POST', $params);
    }

    /**
     * 13. 预约退号
     * @throws \Exception
     */
    public function registrationRefund(string $cardNo, string $sarid, float $receiMoney)
    {
        $handleName = 'bmfw.appt.regist.refund';
        $params = [
            'handleName' => $handleName,
            'companyPartyId' => $this->companyPartyId,
            'configId' => $this->configId,
            'cardNo' => $cardNo,
            'sarid' => $sarid,
            'receiMoney' => $receiMoney,
        ];
        return $this->hisRequest('POST', $params);
    }

    /**
     * 10. 挂号支付结算
     * @throws \Exception
     */
    public function registrationPay(string $cardNo, string $sarid, float $receiMoney, string $payMethod = '同步结算')
    {
        $handleName = 'bmfw.appt.regist.pay';
        $params = [
            'handleName' => $handleName,
            'companyPartyId' => $this->companyPartyId,
            'configId' => $this->configId,
            'cardNo' => $cardNo,
            'sarid' => $sarid,
            'receiMoney' => $receiMoney,
            'payMethod' => $payMethod,
//            'payId' => ''
        ];
        return $this->hisRequest('POST', $params);
    }

    /**
     * 7. 设置就诊人默认就诊人
     * @param string $openid
     * @param string $cardNo
     * @return mixed
     * @throws \Exception
     */
    public function userSetDefaultPatient(string $openid, string $cardNo): mixed
    {
        $handleName = 'bmfw.patient.default';
        $params = [
            'handleName' => $handleName,
            'companyPartyId' => $this->companyPartyId,
            'configId' => $this->configId,
            'openid' => $openid,
            'cardNo' => $cardNo,
        ];
        return $this->hisRequest('POST', $params);
    }

    /**
     * 8. 就诊人解除绑定
     * @param string $openid
     * @param string $cardNo
     * @return mixed
     * @throws \Exception
     */
    public function userUnbindPatient(string $openid, string $cardNo): mixed
    {
        $handleName = 'bmfw.patient.bind.remove';
        $params = [
            'handleName' => $handleName,
            'companyPartyId' => $this->companyPartyId,
            'configId' => $this->configId,
            'openid' => $openid,
            'cardNo' => $cardNo,
        ];
        return $this->hisRequest('POST', $params);
    }

    /**
     *  9. 查询绑定的就诊人列表
     * @param string $openid
     * @return mixed
     * @throws \Exception
     */
    public function getUserPatientList(string $openid): mixed
    {
        $handleName = 'bmfw.patient.list';
        $params = [
            'handleName' => $handleName,
            'companyPartyId' => $this->companyPartyId,
            'configId' => $this->configId,
            'openid' => $openid,
        ];
        return $this->hisRequest('POST', $params);
    }


    /**
     * 13. 查询个人挂号列表
     * @param string $phone
     * @param string $cardNo
     * @return mixed
     * @throws \Exception
     */
    public function getRegistrationList(string $phone, string $cardNo): mixed
    {
        $handleName = 'bmfw.appt.regist.query';
        $params = [
            'handleName' => $handleName,
            'companyPartyId' => $this->companyPartyId,
            'configId' => $this->configId,
            'phone' => $phone,
            'cardNo' => $cardNo,
        ];
        return $this->hisRequest('POST', $params);
    }

    /**
     * 15. 查询就诊人门诊缴费记录
     * @param string $cardNo
     * @return mixed
     * @throws \Exception
     */
    public function getPatientPres(string $cardNo): mixed
    {
        $handleName = 'bmfw.patient.pres.query';
        $params = [
            'handleName' => $handleName,
            'companyPartyId' => $this->companyPartyId,
            'configId' => $this->configId,
            'cardNo' => $cardNo,
        ];
        return $this->hisRequest('POST', $params);
    }

    /**
     * 16. 处方收费结算（余额支付）
     * @param string $cardNo
     * @param string $hisOrderNo
     * @param string $hisOrderType
     * @param string $payMethod
     * @param string $payAmount
     * @return mixed
     * @throws \Exception
     */
    public function patientPresPay(string $cardNo, string $hisOrderNo, string $hisOrderType, string $payMethod, string $payAmount): mixed
    {
        $handleName = 'bmfw.patient.pres.query.pay';
        $params = [
            'handleName' => $handleName,
            'companyPartyId' => $this->companyPartyId,
            'configId' => $this->configId,
            'cardNo' => $cardNo,
            'hisOrderNo' => json_encode([
                [
                    'hisOrderNo' => $hisOrderNo,
                    'hisOrderType' => $hisOrderType
                ]
            ]),
            'payMethod' => $payMethod,
            'payamount' => $payAmount,
        ];
        return $this->hisRequest('POST', $params);
    }

    /**
     * 17. 获取缴费记录
     * @param string $idCard
     * @param string $beginDate
     * @param string $endDate
     * @return mixed
     * @throws \Exception
     */
    public function getPaymentList(string $idCard, string $beginDate = '', string $endDate = ''): mixed
    {
        $handleName = 'bmfw.patient.pres.pay.query';
        $params = [
            'handleName' => $handleName,
            'companyPartyId' => $this->companyPartyId,
            'configId' => $this->configId,
            'idCardNo' => $idCard,
            'beginDate' => $beginDate,
            'endDate' => $endDate,
        ];
        return $this->hisRequest('POST', $params);
    }

    /**
     * 21. 获取处方列表
     * @param string $idCard
     * @param string $beginDate
     * @param string $endDate
     * @return mixed
     * @throws \Exception
     */
    public function getPrescriptionList(string $idCard, string $beginDate = '', string $endDate = ''): mixed
    {
        $handleName = 'bmfw.patient.pres.medicine.query';
        $params = [
            'handleName' => $handleName,
            'companyPartyId' => $this->companyPartyId,
            'configId' => $this->configId,
            'idCardNo' => $idCard,
            'beginDate' => $beginDate,
            'endDate' => $endDate,
        ];
        return $this->hisRequest('POST', $params);
    }

    /**
     * 33. 就诊人编号及证件号互查
     * @param string $idCardNo
     * @param string $cardNo
     * @return mixed
     * @throws \Exception
     */
    public function getPatientCardNumberInfo(string $idCardNo = '', string $cardNo = ''): mixed
    {
        $handleName = 'mp.exec/bmfw.patient.map';
        $params = [
            'handleName' => $handleName,
            'companyPartyId' => $this->companyPartyId,
            'configId' => $this->configId,
            'idCardNo' => $idCardNo,
            'cardNo' => $cardNo,
        ];
        return $this->hisRequest('POST', $params);
    }

    /**
     * 34.全部档案信息
     * @throws \Exception
     */
    public function getHisAllPatientList()
    {
        $handleName = 'mp.exec/bmfw.his.patient.list';
        $params = [
            'handleName' => $handleName,
            'companyPartyId' => $this->companyPartyId,
            'configId' => $this->configId
        ];
        return $this->hisRequest('POST', $params);
    }

    /**
     * 35. 病人就诊记录查询
     * @throws \Exception
     */
    public function getAttendanceRecordsList(string $cardNo)
    {
        $handleName = 'mp.exec/bmfw.his.jzjl.list';
        $params = [
            'handleName' => $handleName,
            'companyPartyId' => $this->companyPartyId,
            'configId' => $this->configId,
            'cardNo' => $cardNo,
        ];
        return $this->hisRequest('POST', $params);
    }

    /**
     * 36.病人就诊记录病历查询
     * @throws \Exception
     */
    public function getMedicalRecordList(string $recordId)
    {
        $handleName = 'mp.exec/bmfw.his.jzjl.bl.list';
        $params = [
            'handleName' => $handleName,
            'companyPartyId' => $this->companyPartyId,
            'configId' => $this->configId,
            'recordId' => $recordId,
        ];
        return $this->hisRequest('POST', $params);
    }


    /**
     * 38.查询病人已作废结算记录
     * @throws \Exception
     */
    public function getInvalidSettlementList(string $cardNo = '', string $hisOrderNo = '')
    {
        $handleName = 'mp.exec/bmfw.patient.pres.detele.list';
        $params = [
            'handleName' => $handleName,
            'companyPartyId' => $this->companyPartyId,
            'configId' => $this->configId,
            'cardNo' => $cardNo,
            'hisOrderNo' => $hisOrderNo,
        ];
        return $this->hisRequest('POST', $params);
    }

    /**
     * 处理医生头像
     * @param array $doctorList
     * @param string $deptId
     * @return array
     */
    protected function actionDoctorAvatar(array &$doctorList, string $deptId): array
    {
        if (empty($doctorList['doctorList'])) {
            return $doctorList;
        }
        $service = make(HisImageService::class);
        foreach ($doctorList['doctorList'] as &$doctor) {
            if (empty($doctor['headImage'])) {
                continue;
            }
            $doctor['headImage'] = $service->getDoctorHeadImage($this->companyPartyId, $deptId, $doctor['doctorCd'], $doctor['headImage'], $doctor['doctorName']);
        }

        return $doctorList;
    }

    /**
     * 处理科室图片
     * @param array $departmentList
     * @return array
     */
    protected function actionDepartmentPicture(array &$departmentList): array
    {
        if (empty($departmentList['deptList'])) {
            return $departmentList;
        }
        $service = make(HisImageService::class);
        foreach ($departmentList['deptList'] as &$department) {
            if (empty($department['deptPic'])) {
                continue;
            }
            $department['deptPic'] = $service->getDepartmentImage($this->companyPartyId, $department['deptId'], $department['deptPic']);
        }
        return $departmentList;
    }


    /**
     * HIS 公共请求
     * @param string $method
     * @param array $params
     * @return mixed
     * @throws \Exception
     */
    protected function hisRequest(string $method = 'POST', array $params = []): mixed
    {
        $client = new Client([
                'base_uri' => $this->domain,
                'timeout' => 5.0,
                'verify' => false,
                'headers' => ['Content-Type' => 'application/json']]
        );
        $url = '';
        $requestData = [];
        if ($this->version == 'C') {
            $requestData = [
                'strJson' => json_encode($params)
            ];
            $url = $this->curl;
        }
        if ($this->version == 'J') {
            $requestData = $params;
            $url = $this->jurl . '/' . $params['handleName'];
            unset($requestData['handleName']);
        }
        try {
            $response = $client->request($method, $url, [
                'json' => $requestData,
                'on_stats' => function (TransferStats $stats) {
                    $request = $stats->getRequest();
                    $response = $stats->getResponse();
                    // 记录请求信息
                    $logData = [
                        'request' => [
                            'method' => $request->getMethod(),
                            'uri' => (string)$request->getUri(),
                            'headers' => $request->getHeaders(),
                            'body' => (string)$request->getBody(),
                        ],
                        'response' => [
                            'status_code' => $response?->getStatusCode(),
                            'headers' => $response?->getHeaders(),
                            'body' => $response ? (string)$response->getBody() : null,
                        ],
                        'transfer_time' => $stats->getTransferTime(),
                    ];

                    // 使用日志库记录日志，例如 Monolog
                    $this->logger->info('HIS API Request', $logData);
                }
            ]);
            $result = json_decode($response->getBody()->getContents(), true);
            return is_string($result) ? json_decode($result, true) : $result;
        } catch (GuzzleException $e) {
            throw new \Exception($e->getMessage());
        }
    }
}
