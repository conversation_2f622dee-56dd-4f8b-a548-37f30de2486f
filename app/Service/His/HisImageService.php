<?php

namespace App\Service\His;

use App\Model\LyAttachment;
use GuzzleHttp\Exception\GuzzleException;
use Hyperf\Logger\LoggerFactory;
use Hyperf\Redis\Redis;
use Psr\Log\LoggerInterface;
use function Hyperf\Config\config;
use function Hyperf\Support\env;

class HisImageService
{
    protected Redis $redis;
    protected LoggerInterface $logger;

    public function __construct(
        Redis $redis,
        LoggerFactory $loggerFactory
    )
    {
        $this->redis = $redis;
        $this->logger = $loggerFactory->get('log', 'default');
    }

    /**
     * 获取医生头像
     * @param string $companyPartyId
     * @param string $deptId
     * @param string $doctorCd
     * @param string $headImage
     * @param string $doctorName
     * @return string|null
     */
    public function getDoctorHeadImage(string $companyPartyId, string $deptId, string $doctorCd, string $headImage, string $doctorName): ?string
    {
        // 从 redis 获取头像
        $avatar = $this->getImageForRedisByKey($companyPartyId, $deptId, $doctorCd);
        if ($avatar) return $avatar;
        // 从  mysql 获取头像
        $avatar = $this->getImageForDatabase($companyPartyId, $deptId, $doctorCd);
        if ($avatar) return $avatar;
        // 保存头像返回
        return $this->saveImage($companyPartyId, $deptId, $headImage, $doctorCd, $doctorName);
    }

    /**
     * 获取科室图片
     * @param string $companyPartyId
     * @param string $deptId
     * @param string $headImage
     * @return string|null
     */
    public function getDepartmentImage(string $companyPartyId, string $deptId, string $deptPic): ?string
    {
        // 从 redis 获取头像
        $image = $this->getImageForRedisByKey($companyPartyId, $deptId);
        if ($image) return $image;
        // 从  mysql 获取头像
        $image = $this->getImageForDatabase($companyPartyId, $deptId);
        if ($image) return $image;
        // 保存头像返回
        return $this->saveImage($companyPartyId, $deptId, $deptPic);
    }

    /**
     * 从 Redis 检查是否有医生头像
     * @param string $deptId
     * @param string $doctorCd
     * @return string|null
     */
    public function getImageForRedisByKey(string $companyPartyId, string $deptId, string $doctorCd = ''): ?string
    {
        $key = $companyPartyId . '-' . $deptId;
        if ($doctorCd) {
            // 兼容科室图片的查询获取
            $key = $key . '-' . $doctorCd;
        }
        if ($this->redis->exists($key)) {
            return $this->redis->get($key); //图片的本地路径
        }
        return null;
    }

    /**
     * 从数据库检查是否有医生头像
     * @param string $deptId
     * @param string $doctorCd
     * @return string|null
     */
    public function getImageForDatabase(string $companyPartyId, string $deptId, string $doctorCd = ''): ?string
    {
        $model = new LyAttachment();
        $where = [
            'company_party_id' => $companyPartyId,
            'dept_id' => $deptId,
            'status' => 1
        ];
        if ($doctorCd) {
            $where['doctor_cd'] = $doctorCd;
        }
        $existingRecord = $model->where($where)->first();
        if ($existingRecord) {
            $imagePath = $existingRecord->url;
            $key = $companyPartyId . '-' . $deptId;
            if ($doctorCd) {
                $key = $key . '-' . $doctorCd;
            }
            $cacheExpiry = 86400;
            $this->redis->set($key, $imagePath, $cacheExpiry);
            return $imagePath;
        }
        return null;
    }

    /**
     * 保存医生头像
     * @param string $companyPartyId
     * @param string $deptId
     * @param string $doctorCd
     * @param string $headImage
     * @param string $doctorName
     * @return string|null
     * @throws GuzzleException
     */
    public function saveImage(string $companyPartyId, string $deptId, string $headImage, string $doctorCd = '', string $doctorName = ''): ?string
    {
        $uploadPath = config('upload_path');
        $imageData = base64_decode($headImage);
        $key = $companyPartyId . '/' . $deptId . '/';
        if ($doctorCd) {
            $key = $key . $doctorCd . '/';
        }
        $filenameStr = $companyPartyId . '-' . $deptId;
        if ($filenameStr) {
            $filenameStr = $filenameStr . '-' . $doctorCd;
        }
        $filename = md5($filenameStr) . '.png';
        $imagePath = '/department/picture/' . $key . $filename;
        if ($doctorCd) {
            $imagePath = '/doctor/avatar/' . $key . $filename;
        }
        $fullPath = $uploadPath . $imagePath;

        $dirPath = dirname($fullPath);
        if (!is_dir($dirPath)) {
            mkdir($dirPath, 0755, true);
        }

        // Save image
        try {
            file_put_contents($fullPath, $imageData);
            chmod($fullPath, 0644); // Ensure file is readable
        } catch (\Exception $e) {
            $this->logger->error('保存图片失败: ' . $e->getMessage());
            return null;
        }
        $imageHash = md5($headImage);

        $model = new LyAttachment();
        $model->company_party_id = $companyPartyId;
        $model->dept_id = $deptId;
        $model->doctor_cd = $doctorCd ?? '';
        $model->doctor_name = $doctorName ?? '';
        $model->image_base = $headImage;
        $model->image_path = $imagePath;
        $model->hash = $imageHash;
        $model->status = 1;
        // 上传图片到远程服务器
//        $upload = $this->uploadImage($fullPath);
//        if ($upload) {
//            $model->url = $upload['url'];
//        }
        $model->url = config('upload_domain') . '/uploads' . $imagePath;
        $model->save();

        $cacheExpiry = 86400;
        $this->redis->set($filenameStr, $model->url, $cacheExpiry);

        return $model->url;
    }

    /**
     * 上传文件
     * @param $file
     * @return string[]|null
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function uploadImage($file): ?array
    {
        $url = '/api/registered/upload/image';
        $dirPath = dirname($file);
        $dirPath = trim(str_replace(BASE_PATH . '/public', '', $dirPath), '/');
        $domain = config('upload_domain');
        try {
            $client = new \GuzzleHttp\Client([
                'base_uri' => $domain,
                'timeout' => 30,
                'verify' => false,
            ]);

            $response = $client->request('POST', $url, [
                'multipart' => [
                    [
                        'name' => 'type',
                        'contents' => 'file'
                    ],
                    [
                        'name' => 'file',
                        'contents' => file_get_contents($file),
                        'filename' => basename($file)
                    ],
                    [
                        'name' => 'path',
                        'contents' => $dirPath
                    ],
                    [
                        'name' => 'uuid',
                        'contents' => '6c9b56885ebd4812b0fea37328e8f63f'
                    ]
                ]
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            if (isset($result['status']) && $result['status'] == 200) {
                return [
                    'path' => $result['data']['path'] ?? '',
                    'url' => $result['data']['url'] ?? ''
                ];
            }

            $this->logger->error('上传图片失败: ' . ($result['msg'] ?? '未知错误'));
            return null;
        } catch (\Exception $e) {
            $this->logger->error('上传图片异常: ' . $e->getMessage());
            return null;
        }
    }
}
