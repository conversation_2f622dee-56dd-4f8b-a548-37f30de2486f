<?php

namespace App\Service\Ocr;

use Hyperf\Di\Exception\Exception;
use TencentCloud\Common\Credential;
use TencentCloud\Common\Exception\TencentCloudSDKException;
use TencentCloud\Common\Profile\ClientProfile;
use TencentCloud\Common\Profile\HttpProfile;
use TencentCloud\Ocr\V20181119\Models\GeneralAccurateOCRResponse;
use TencentCloud\Ocr\V20181119\Models\GeneralFastOCRRequest;
use TencentCloud\Ocr\V20181119\Models\TableOCRRequest;
use TencentCloud\Ocr\V20181119\OcrClient;
use TencentCloud\Ocr\V20181119\Models\ExtractDocMultiRequest;

class TencentOcrService implements OcrInterface
{

    public function ocrIdentify(array $params): array
    {
        try {
            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
            // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性
            // 以下代码示例仅供参考，建议采用更安全的方式来使用密钥
            // 请参见：https://cloud.tencent.com/document/product/1278/85305
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
//            $cred = new Credential(getenv("TENCENTCLOUD_SECRET_ID"), getenv("TENCENTCLOUD_SECRET_KEY"));
            $cred = new Credential('AKIDLWSChHfoiLiodBSUGm22sRjkVzED2buw', 'oMehU0GF0VkkVAdrpXyA7rOffPt75mCN');
            // 使用临时密钥示例
            // $cred = new Credential("SecretId", "SecretKey", "Token");
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            $httpProfile = new HttpProfile();
            $httpProfile->setEndpoint("ocr.tencentcloudapi.com");

            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            $clientProfile = new ClientProfile();
            $clientProfile->setHttpProfile($httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            $client = new OcrClient($cred, "", $clientProfile);

            // 实例化一个请求对象,每个接口都会对应一个request对象
            $req = new ExtractDocMultiRequest();

            $requestParams = [
                'Action' => 'ExtractDocMulti',
                'Version' => '2018-11-19',
                'ImageBase64' => $params['imageBase64'] ?? '',
                'ImageUrl' => $params['imageUrl'] ?? '',
                'ReturnFullText' => false,
                'ConfigId' => 'Table',
                'ItemNamesShowMode' => true,
                'EnableCoord' => false,
            ];
            $req->fromJsonString(json_encode($requestParams));

            // 返回的resp是一个ExtractDocMultiResponse的实例，与请求对象对应
            $resp = $client->ExtractDocMulti($req);

            // 输出json格式的字符串回包
            return json_decode($resp->toJsonString(), true);
        } catch (TencentCloudSDKException $e) {
            throw new Exception($e->getMessage());
        }
    }
}
