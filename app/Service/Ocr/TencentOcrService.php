<?php

namespace App\Service\Ocr;

use GuzzleHttp\Exception\GuzzleException;
use Hyperf\Guzzle\ClientFactory;
use Psr\Log\LoggerInterface;

class TencentOcrService implements OcrInterface
{
    protected ClientFactory $clientFactory;
    protected LoggerInterface $logger;

    public function __construct(ClientFactory $clientFactory, LoggerInterface $logger)
    {
        $this->clientFactory = $clientFactory;
        $this->logger = $logger;
    }

    /**
     * 腾讯云 OCR 识别
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public function ocrIdentify(array $params): array
    {
        $requestData = [
            'ImageUrl' => $params['imageUrl'] ?? '',
            'ImageBase64' => $params['imageBase64'] ?? '',
            'ReturnFullText' => false,
            'ConfigId' => 'Table',
            'ItemNamesShowMode' => true,
            'EnableCoord' => false,
        ];

        // 从配置或环境变量获取敏感信息
        $secret_id = 'AKIDLWSChHfoiLiodBSUGm22sRjkVzED2buw'; // 或从配置获取
        $secret_key = env('TENCENT_CLOUD_SECRET_KEY');

        if (empty($secret_id) || empty($secret_key)) {
            throw new \Exception('腾讯云API密钥未配置');
        }

        // 常量定义提取到类常量或配置文件
        $service = "ocr";
        $host = "ocr.tencentcloudapi.com";
        $version = "2018-11-19";
        $action = "ExtractDocMulti";
        $algorithm = "TC3-HMAC-SHA256";

        $payload = json_encode($requestData ?? []);
        $timestamp = time();
        $date = gmdate("Y-m-d", $timestamp);

        // 签名计算可以提取为单独的方法
        $signature = $this->calculateSignature($secret_key, $service, $date, [
            'http_request_method' => 'POST',
            'canonical_uri' => '/',
            'host' => $host,
            'action' => $action,
            'payload' => $payload,
            'timestamp' => $timestamp,
        ]);

        $credential_scope = "$date/$service/tc3_request";
        $authorization = "$algorithm Credential=$secret_id/$credential_scope, SignedHeaders=content-type;host;x-tc-action, Signature=$signature";

        $headers = [
            "Authorization" => $authorization,
            "Content-Type" => "application/json; charset=utf-8",
            "Host" => $host,
            "X-TC-Action" => $action,
            "X-TC-Timestamp" => $timestamp,
            "X-TC-Version" => $version
        ];

        try {
            $client = $this->clientFactory->create([
                'base_uri' => "https://{$host}",
                'timeout' => 30.0,
                'verify' => true, // 生产环境应启用SSL验证
                'headers' => $headers
            ]);

            $response = $client->post('/', ['body' => $payload]);

            $responseBody = $response->getBody()->getContents();
            $result = json_decode($responseBody, true) ?? [];

            $this->logger->info('Tencent OCR API Response', [
                'status_code' => $response->getStatusCode(),
                'response' => $result
            ]);

            return $result;

        } catch (GuzzleException $e) {
            $this->logError('Tencent OCR API Error', $e);
            throw new \Exception('OCR API请求失败: ' . $e->getMessage());
        } catch (\Exception $e) {
            $this->logError('Tencent OCR Service Error', $e);
            throw new \Exception('OCR服务异常: ' . $e->getMessage());
        }
    }

    /**
     * 计算签名
     */
    private function calculateSignature(string $secretKey, string $service, string $date, array $params): string
    {
        // 步骤1：拼接规范请求串
        $canonical_headers = "content-type:{$params['ct'] ?? 'application/json; charset=utf-8'}\n"
            . "host:{$params['host']}\n"
            . "x-tc-action:" . strtolower($params['action']) . "\n";

        $hashed_payload = hash("sha256", $params['payload']);
        $canonical_request = "{$params['http_request_method']}\n{$params['canonical_uri']}\n\n"
            . "{$canonical_headers}\ncontent-type;host;x-tc-action\n{$hashed_payload}";

        // 步骤2：拼接待签名字符串
        $credential_scope = "$date/$service/tc3_request";
        $string_to_sign = "TC3-HMAC-SHA256\n{$params['timestamp']}\n"
            . "$credential_scope\n" . hash("sha256", $canonical_request);

        // 步骤3：计算签名
        $secret_date = hash_hmac("sha256", $date, "TC3" . $secretKey);
        $secret_service = hash_hmac("sha256", $service, $secret_date);
        $secret_signing = hash_hmac("sha256", "tc3_request", $secret_service);

        return hash_hmac("sha256", $string_to_sign, $secret_signing);
    }

    /**
     * 记录错误日志
     */
    private function logError(string $title, \Throwable $e): void
    {
        $this->logger->error($title, [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]);
    }

}
