<?php

declare(strict_types=1);

namespace App\Model;


/**
 * @property int $id
 * @property int $serial_num
 * @property string $title
 * @property string $options
 * @property int $status
 * @property int $type
 * @property int $dimension
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class LyScale extends Model
{
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'ly_scales';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id',
        'serial_num',
        'title',
        'options',
        'status',
        'type',
        'dimension',
        'created_at',
        'updated_at'
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = ['id' => 'integer', 'serial_num' => 'integer', 'status' => 'integer', 'type' => 'integer', 'dimension' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    public function getOptionsAttribute($value)
    {
        return $value ? json_decode($value, true) : [];
    }
}
