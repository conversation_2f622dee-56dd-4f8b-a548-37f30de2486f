<?php

declare(strict_types=1);

namespace App\Model;



/**
 * @property int $id 
 * @property string $ak 
 * @property string $report_uuid 
 * @property string $openid 
 * @property string $gender 
 * @property int $age 
 * @property string $answer 
 * @property string $physiology_score 
 * @property string $psychology_score 
 * @property string $society_score 
 * @property string $synthesis_score 
 * @property string $physiology_report 
 * @property string $psychology_report 
 * @property string $society_report 
 * @property string $synthesis_report 
 * @property string $physiology_diagnosis 
 * @property string $psychology_diagnosis 
 * @property string $society_diagnosis 
 * @property string $synthesis_diagnosis 
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 * @property string $status 
 * @property int $process 
 * @property string $remark 
 * @property string $status_memo 
 */
class LyScalesReport extends Model
{
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'ly_scales_report';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id',
        'ak',
        'report_uuid',
        'openid',
        'gender',
        'age',
        'answer',
        'physiology_score',
        'psychology_score',
        'society_score',
        'synthesis_score',
        'physiology_report',
        'psychology_report',
        'society_report',
        'synthesis_report',
        'status',
        'process',
        'remark',
        'created_at',
        'updated_at',
        'status_memo'
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = ['id' => 'integer', 'age' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime', 'process' => 'integer'];

    protected ?string $dateFormat = 'U';
}
