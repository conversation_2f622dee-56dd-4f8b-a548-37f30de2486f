<?php

declare(strict_types=1);

namespace App\Model;



/**
 * @property int $id
 * @property string $company_party_id
 * @property string $dept_id
 * @property string $doctor_cd
 * @property string $doctor_name
 * @property string $image_base
 * @property string $image_path
 * @property string $hash
 * @property string $url
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property int $status
 */
class LyAttachment extends Model
{
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'ly_attachment';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id',
        'company_party_id',
        'dept_id',
        'doctor_cd',
        'doctor_name',
        'image_base',
        'image_path',
        'hash',
        'status',
        'url'
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = ['id' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime', 'status' => 'integer'];

    protected ?string $dateFormat = 'U';
}
