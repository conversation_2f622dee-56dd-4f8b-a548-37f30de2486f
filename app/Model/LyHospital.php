<?php

declare(strict_types=1);

namespace App\Model;



/**
 * @property int $id
 * @property string $company_party_id
 * @property string $name
 * @property string $ak
 * @property string $sk
 * @property string $desc
 * @property string $info
 * @property string $address
 * @property string $longitude
 * @property string $latitude
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property int $status
 * @property string $appid
 * @property string $config_id
 * @property string $contry_company_sn
 * @property string $domain
 * @property string $version
 * @property string $app_code
 */
class LyHospital extends Model
{
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'ly_hospital';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = ['id' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime', 'status' => 'integer'];
}
