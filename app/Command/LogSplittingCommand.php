<?php

declare(strict_types=1);

namespace App\Command;

use DateTime;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Psr\Container\ContainerInterface;

#[Command]
class LogSplittingCommand extends HyperfCommand
{
    public function __construct(protected ContainerInterface $container)
    {
        parent::__construct('LogSplitting:command');
    }

    public function configure(): void
    {
        parent::configure();
        $this->setDescription('Log splitting Command');
    }

    public function handle()
    {
        $logPath = BASE_PATH . '/logs';
        // hyperf-2025-05-26.log

        // Get all log files in the directory
        $logFiles = glob($logPath . '/hyperf-*.log');

        // Calculate the cutoff date (3 days ago)
        $cutoffDate = new DateTime();
        $cutoffDate->modify('-3 days');

        foreach ($logFiles as $file) {
            // Extract date from filename (format: hyperf-YYYY-MM-DD.log)
            if (preg_match('/hyperf-(\d{4}-\d{2}-\d{2})\.log$/', basename($file), $matches)) {
                $fileDate = DateTime::createFromFormat('Y-m-d', $matches[1]);

                // If the file date is before the cutoff date, delete it
                if ($fileDate < $cutoffDate) {
                    unlink($file);
                    echo "Deleted old log file: " . basename($file) . "\n";
                }
            }
        }

        $this->line('Hello Hyperf!', 'info');
    }
}
