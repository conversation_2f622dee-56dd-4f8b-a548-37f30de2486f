<?php

declare(strict_types=1);

namespace App\Request;

use Hyperf\Validation\Request\FormRequest;
use Hyperf\Validation\Rule;

class ScalesReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'reportUuid' => ['required'],
            'openid' => ['string', 'max:255'],
            'gender' => ['required', Rule::in('男', '女')],
            'age' => ['required', 'integer'],
            'answer' => ['required'],
        ];
    }

    public function messages(): array
    {
        return [
            'reportUuid.required' => '报告UUID不能为空',
            'openid.string' => 'openid为字符串类型',
            'gender.required' => '性别不能为空',
            'gender.in' => '性别只能为男或者女',
            'age.required' => '年龄不能为空',
            'age.integer' => '年龄为整数类型',
            'answer.required' => '答案不能为空',
        ];
    }

}
