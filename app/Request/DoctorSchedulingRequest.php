<?php

declare(strict_types=1);

namespace App\Request;

use Hyperf\Validation\Request\FormRequest;

class DoctorSchedulingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'deptId' => ['required'],
            'doctorCd' => ['required'],
            'startTime' => ['date_format:Y-m-d'],
        ];
    }

    public function messages(): array
    {
        return [
            'deptId.required' => '科室ID参数必须',
            'doctorCd.required' => '医生编号参数必须',
            'startTime.date_format' => '时间选择为日期格式：YYYY-MM-DD',
        ];
    }
}
