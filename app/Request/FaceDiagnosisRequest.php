<?php

declare(strict_types=1);

namespace App\Request;

use Hyperf\Validation\Request\FormRequest;
use Hyperf\Validation\Rule;

class FaceDiagnosisRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'scene' => ['required', 'string', Rule::in('1', '2')], // 推荐 1
            'ffImage' => 'string',
            'tfImage' => 'string',
            'tbImage' => 'string',
            'gender' => ['required', Rule::in('男', '女')],
        ];
    }
}
