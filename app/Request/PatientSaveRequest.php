<?php

declare(strict_types=1);

namespace App\Request;

use Hyperf\Validation\Request\FormRequest;
use Hyperf\Validation\Rule;

class PatientSaveRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'cardNo' => ['string'], // 空表示新增，不为空表示修改
            'idCardNo' => ['required', 'regex:/^\d{17}[\dXx]$/'],
            'name' => ['required'],
            'address' => ['required'],
            'openid' => ['required'],
            'sexTypeLabel' => ['required', Rule::in('男', '女')],
            'phoneNumber' => ['required', 'regex:/^1\d{10}$/'],
            'birthday' => ['required','date_format:Y-m-d'],
            'isChild' => ['required', Rule::in('Y', 'N')],  // Y 是儿童, 'N' 不是儿童
            'nationLabel' => ['string'],
            'linkType' => ['string', Rule::in('父子', '母子', '妇女', '母女')],
            'jhridNumber' => ['string', 'regex:/^\d{17}[\dXx]$/'],
            'jhrPhone' => ['string', 'regex:/^1\d{10}$/'],
            'jhrname' => ['string'],
        ];
    }

    public function messages(): array
    {
        return [
            'cardNo.string' => '档案编号为字符串类型',
            'idCardNo.required' => '身份证不能为空',
            'idCardNo.regex' => '身份证号码格式不正确',
            'name.required' => '姓名不能为空',
            'address.required' => '地址不能为空',
            'openid.required' => '用户OPENID不能为空',
            'sexTypeLabel.required' => '性别不能为空',
            'phoneNumber.required' => '手机号不能为空',
            'phoneNumber.regex' => '手机号码格式不正确',
            'birthday.required' => '出生日期不能为空',
            'birthday.date_format' => '出生日期格式不正确',
            'isChild.required' => '是否儿童不能为空',
            'isChild.in' => '是否儿童只能为Y或者N',
            'nationLabel.string' => '民族不能为空',
            'linkType.string' => '监护人关系为字符串类型',
            'jhridNumber.string' => '监护人证件号码为字符串类型',
            'jhridNumber.regex' => '监护人身份证号码格式不正确',
            'jhrPhone.string' => '监护人手机号为字符串类型',
            'jhrPhone.regex' => '监护人手机号码格式不正确',
            'jhrname.string' => '监护人姓名为字符串类型',
        ];
    }
}
