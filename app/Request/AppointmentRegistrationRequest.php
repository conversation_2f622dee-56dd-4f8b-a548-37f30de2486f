<?php

declare(strict_types=1);

namespace App\Request;

use Hyperf\Validation\Request\FormRequest;

class AppointmentRegistrationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'deptId' => ['required'],
            'startTime' => ['required', 'date_format:Y-m-d'],
            'planNumUuid' => ['required'],
            'cardNo' => ['required'],
            'idCardNo' => ['required'],
            'patiName' => ['required'],
            'phone' => ['required'],
            'endPeriod' => ['required', 'date_format:H:i'],
            'startPeriod' => ['required', 'date_format:H:i'],
        ];
    }

    public function messages(): array
    {
        return [
            'deptId.required' => '科室ID参数必须',
            'startTime.required' => '预约日期必须',
            'startTime.date_format' => '预约日期为日期格式：YYYY-MM-DD',
            'planNumUuid.required' => '排班ID必须',
            'cardNo.required' => '病历编号必须',
            'idCardNo.required' => '身份证号必须',
            'patiName.required' => '就诊人姓名必须',
            'phone.required' => '就诊人联系电话必须',
            'endPeriod.required' => '预约时间段结束时间必须',
            'endPeriod.date_format' => '预约时间段结束时间格式：HH:ss',
            'startPeriod.required' => '预约时间段开始时间必须',
            'startPeriod.date_format' => '预约时间段开始时间格式：HH:ss',
        ];
    }
}
