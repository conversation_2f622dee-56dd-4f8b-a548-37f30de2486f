<?php

declare(strict_types=1);

namespace App\Request;

use Hyperf\Validation\Request\FormRequest;

class RegistrationPayRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'sarid' => ['required'],
            'cardNo' => ['required'],
            'receiMoney' => ['required'],
            'payMethod' => ['string'],
            'payId' => ['string'],
        ];
    }

    public function messages(): array
    {
        return [
            'sarid.required' => '预约排班ID不能为空',
            'cardNo.required' => '就诊人编号不能为空',
            'receiMoney.required' => '退号金额不能为空',
            'payMethod.string' => '支付类型字符串类型',
            'payId.string' => '支付ID字符串类型',
        ];
    }
}
