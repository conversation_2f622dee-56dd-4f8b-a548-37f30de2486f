<?php

declare(strict_types=1);

namespace App\Request;

use Hyperf\Validation\Request\FormRequest;

class PatientSetDefaultRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'cardNo' => ['required'],
            'openid' => ['required'],
        ];
    }

    public function messages(): array
    {
        return [
            'cardNo.required' => '档案编号不能为空',
            'openid.required' => '用户openid不能为空',
        ];
    }
}
