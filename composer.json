{"name": "hyperf/hyperf-skeleton", "type": "project", "keywords": ["php", "swoole", "framework", "hyperf", "microservice", "middleware"], "description": "A coroutine framework that focuses on hyperspeed and flexible, specifically use for build microservices and middlewares.", "license": "Apache-2.0", "require": {"php": ">=8.1", "duncan3dc/blade": "^4.14", "hyperf/amqp": "~3.1.0", "hyperf/async-queue": "^3.1", "hyperf/cache": "~3.1.0", "hyperf/command": "^3.1", "hyperf/config": "^3.1", "hyperf/constants": "~3.1.0", "hyperf/crontab": "^3.1", "hyperf/database": "~3.1.0", "hyperf/db-connection": "~3.1.0", "hyperf/engine": "^2.10", "hyperf/filesystem": "^3.1", "hyperf/framework": "~3.1.0", "hyperf/guzzle": "~3.1.0", "hyperf/http-server": "~3.1.0", "hyperf/logger": "^3.1", "hyperf/memory": "~3.1.0", "hyperf/model-cache": "~3.1.0", "hyperf/process": "~3.1.0", "hyperf/redis": "^3.1", "hyperf/tracer": "~3.1.0", "hyperf/utils": "^3.1", "hyperf/validation": "^3.1", "hyperf/view": "^3.1", "hyperf/view-engine": "^3.1", "ramsey/uuid": "^4.7"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "hyperf/devtool": "~3.1.0", "hyperf/testing": "~3.1.0", "mockery/mockery": "^1.0", "phpstan/phpstan": "^1.0", "swoole/ide-helper": "^5.0"}, "suggest": {"ext-openssl": "Required to use HTTPS.", "ext-json": "Required to use JSON.", "ext-pdo": "Required to use MySQL Client.", "ext-pdo_mysql": "Required to use MySQL Client.", "ext-redis": "Required to use Redis Client."}, "autoload": {"psr-4": {"App\\": "app/"}, "files": []}, "autoload-dev": {"psr-4": {"HyperfTest\\": "./test/"}}, "minimum-stability": "dev", "prefer-stable": true, "config": {"optimize-autoloader": true, "sort-packages": true}, "extra": [], "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-autoload-dump": ["rm -rf runtime/container"], "test": "co-phpunit --prepend test/bootstrap.php --colors=always", "cs-fix": "php-cs-fixer fix $1", "analyse": "phpstan analyse --memory-limit 300M", "start": ["Composer\\Config::disableProcessTimeout", "php ./bin/hyperf.php start"]}}