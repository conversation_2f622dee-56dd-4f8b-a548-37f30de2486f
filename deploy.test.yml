version: '3.7'
services:
  hyperf:
    image: $REGISTRY_URL/$PROJECT_NAME:test
    environment:
      - "APP_PROJECT=hyperf"
      - "APP_ENV=testing"
    ports:
      - "9501:9501"
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 5
      update_config:
        parallelism: 2
        delay: 5s
        order: start-first
    networks:
      - hyperf_net
    configs:
      - source: hyperf_v1.0
        target: /opt/www/.env
configs:
  hyperf_v1.0:
    external: true
networks:
  hyperf_net:
    external: true
